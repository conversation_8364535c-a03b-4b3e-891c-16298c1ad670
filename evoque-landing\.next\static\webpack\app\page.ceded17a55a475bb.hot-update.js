"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ContactForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ContactForm */ \"(app-pages-browser)/./src/components/ContactForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Import only essential components\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimationWrapper, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            ref: containerRef,\n            className: \"bg-white min-h-screen relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RomanticFloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.section, {\n                    className: \"hero-section min-h-screen flex items-center relative z-20 bg-gradient-to-br from-primary-50 via-white to-secondary-50\",\n                    style: {\n                        y: heroY\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-content container mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                className: \"text-center max-w-4xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatePresence, {\n                                    children: isLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    type: \"spring\",\n                                                    stiffness: 100,\n                                                    delay: 0.2\n                                                },\n                                                className: \"mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MorphingWeddingLogo, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                                text: \"Transform Your Venue into a Love Story\",\n                                                className: \"text-5xl md:text-7xl font-playfair font-bold text-primary-800 mb-6 leading-tight\",\n                                                variant: \"romantic\",\n                                                delay: 0.4\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.p, {\n                                                className: \"text-xl md:text-2xl text-gray-600 mb-8 font-light\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.8\n                                                },\n                                                children: \"AI-Powered Marketing That Connects Hearts & Venues\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"#demo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.button, {\n                                                            className: \"px-8 py-4 bg-primary-600 text-white rounded-full font-medium shadow-lg hover:shadow-xl transform transition-all duration-300\",\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"#b8866f\"\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: \"Watch Your Venue Come Alive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"#contact\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.button, {\n                                                            className: \"px-8 py-4 bg-white text-primary-600 rounded-full font-medium shadow-lg hover:shadow-xl transform transition-all duration-300 border-2 border-primary-600\",\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"#fff5f0\"\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: \"Start Free Trial\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                            className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2\",\n                            animate: {\n                                y: [\n                                    0,\n                                    10,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                repeat: Infinity,\n                                duration: 2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    className: \"w-1 h-3 bg-primary-400 rounded-full mt-2\",\n                                    animate: {\n                                        opacity: [\n                                            0,\n                                            1,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        repeat: Infinity,\n                                        duration: 1.5\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"video-section relative h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                        className: \"sticky top-0 w-full h-screen overflow-hidden\",\n                        style: {\n                            opacity: videoOpacity,\n                            scale: videoScale\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    ref: videoRef,\n                                    className: \"w-full h-full object-cover\",\n                                    src: \"/videos/WeddingAgencyVideo.mp4\",\n                                    muted: true,\n                                    loop: true,\n                                    playsInline: true,\n                                    onLoadedData: handleVideoLoad,\n                                    onError: ()=>setVideoError(true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                !videoLoaded && !videoError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 border-4 border-primary-300 border-t-primary-600 rounded-full animate-spin mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600 text-lg\",\n                                                children: \"Loading your venue experience...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center z-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"text-center text-white px-4\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 1,\n                                            delay: 0.5\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl font-playfair mb-4 drop-shadow-2xl\",\n                                                children: \"Where Dreams Become Reality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl font-light drop-shadow-xl\",\n                                                children: \"Showcase your venue like never before\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollRevealSection, {\n                    variant: \"elegant\",\n                    className: \"relative z-30 bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                    text: \"AI-Powered Features for Modern Venues\",\n                                    className: \"text-4xl md:text-6xl font-playfair text-center text-primary-800 mb-16\",\n                                    variant: \"slideIn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"24/7 AI Concierge\",\n                                            description: \"Never miss a lead with intelligent responses that capture hearts\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-primary-400 to-secondary-400\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"Smart Lead Qualification\",\n                                            description: \"Automatically identify and prioritize your perfect couples\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-secondary-400 to-accent-400\",\n                                            delay: 0.4\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"Venue Analytics\",\n                                            description: \"Track what couples love about your venue and optimize\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-accent-400 to-primary-400\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollRevealSection, {\n                    variant: \"bloom\",\n                    className: \"py-20 bg-gradient-to-b from-gray-50 to-white relative z-30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                text: \"Why Venues Choose Evoque\",\n                                variant: \"romantic\",\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-12 text-gray-800\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    {\n                                        value: \"72%\",\n                                        text: \"of couples book venues that respond within 24 hours\",\n                                        color: \"#cb997e\",\n                                        icon: \"⏱️\",\n                                        animation: \"heart-pulse\"\n                                    },\n                                    {\n                                        value: \"89%\",\n                                        text: \"of Gen Z couples research venues online first\",\n                                        color: \"#5c8d89\",\n                                        icon: \"\\uD83D\\uDCBB\",\n                                        animation: \"sparkle-burst\"\n                                    },\n                                    {\n                                        value: \"3.2x\",\n                                        text: \"higher booking rates with automated responses\",\n                                        color: \"#cb997e\",\n                                        icon: \"\\uD83D\\uDCC8\",\n                                        animation: \"wedding-rings-sparkle\"\n                                    },\n                                    {\n                                        value: \"68%\",\n                                        text: \"of venues lose bookings due to slow response times\",\n                                        color: \"#5c8d89\",\n                                        icon: \"\\uD83D\\uDCC9\",\n                                        animation: \"floral-bloom\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GSAPScrollAnimation, {\n                                        animation: \"fadeInUp\",\n                                        delay: index * 0.1,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                            className: \"relative bg-white p-8 rounded-2xl shadow-lg overflow-hidden group\",\n                                            whileHover: {\n                                                y: -5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 opacity-20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LottieAnimation, {\n                                                        animationPath: \"/animations/\".concat(stat.animation, \".json\"),\n                                                        width: 80,\n                                                        height: 80,\n                                                        loop: true,\n                                                        autoplay: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-2\",\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            style: {\n                                                                color: stat.color\n                                                            },\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: stat.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                    className: \"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-300\",\n                                                    style: {\n                                                        background: \"linear-gradient(135deg, \".concat(stat.color, \"20 0%, transparent 100%)\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WidgetDemoSection, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    id: \"demo\",\n                    className: \"py-20 bg-gradient-to-b from-white to-primary-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-12\",\n                                children: \"See Evoque in Action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold mb-4\",\n                                                children: \"Watch How We Transform Venue Marketing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6\",\n                                                children: \"Experience firsthand how our AI-powered platform can revolutionize your venue's online presence and booking process.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"See real conversations with engaged couples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Watch automated tour scheduling in action\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Explore our analytics dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-video bg-gray-200 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Demo Video Placeholder\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VenueImportGuide, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomizableChatWidget, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});