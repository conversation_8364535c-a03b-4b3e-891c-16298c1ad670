"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/animations/ParallaxBackground.tsx":
/*!**********************************************************!*\
  !*** ./src/components/animations/ParallaxBackground.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParallaxBackground: function() { return /* binding */ ParallaxBackground; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ParallaxBackground auto */ \nvar _s = $RefreshSig$();\n\n\nconst ParallaxBackground = (param)=>{\n    let { className = \"\", primaryColor = \"#cb997e\", secondaryColor = \"#5c8d89\", accentColor = \"#ddbea9\", density = \"medium\", speed = \"medium\", weddingThemed = true } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: ref,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    // Convert density to actual number of elements\n    const getElementCount = ()=>{\n        switch(density){\n            case \"low\":\n                return 20;\n            case \"high\":\n                return 60;\n            case \"medium\":\n            default:\n                return 35;\n        }\n    };\n    // Convert speed to numerical values\n    const getSpeedFactor = ()=>{\n        switch(speed){\n            case \"slow\":\n                return 0.5;\n            case \"fast\":\n                return 2;\n            case \"medium\":\n            default:\n                return 1;\n        }\n    };\n    // Wedding themed elements\n    const weddingElements = [\n        \"ring\",\n        \"heart\",\n        \"flower\",\n        \"leaf\",\n        \"dot\",\n        \"circle\",\n        \"arc\"\n    ];\n    // Generate random elements\n    const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const count = getElementCount();\n        const speedFactor = getSpeedFactor();\n        const newElements = Array.from({\n            length: count\n        }).map((_, i)=>{\n            const type = weddingThemed ? weddingElements[Math.floor(Math.random() * weddingElements.length)] : \"circle\";\n            // Weighted color distribution\n            const colorRand = Math.random();\n            let color;\n            if (colorRand < 0.4) {\n                color = primaryColor;\n            } else if (colorRand < 0.7) {\n                color = secondaryColor;\n            } else {\n                color = accentColor;\n            }\n            return {\n                id: i,\n                x: Math.random() * 100,\n                y: Math.random() * 100,\n                size: 10 + Math.random() * 40,\n                rotate: Math.random() * 360,\n                type,\n                color,\n                delay: Math.random() * 5 * speedFactor\n            };\n        });\n        setElements(newElements);\n    }, [\n        density,\n        primaryColor,\n        secondaryColor,\n        accentColor,\n        weddingThemed,\n        speed\n    ]);\n    // Removed scroll-based parallax effect\n    // Render element based on type\n    const renderElement = (element)=>{\n        const { type, size, color } = element;\n        switch(type){\n            case \"ring\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-2 rounded-full\",\n                    style: {\n                        width: size,\n                        height: size,\n                        borderColor: color,\n                        opacity: 0.5 + size / 100\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, undefined);\n            case \"heart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        width: size,\n                        height: size\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute transform rotate-45\",\n                            style: {\n                                width: size / 2,\n                                height: size / 2,\n                                background: color,\n                                bottom: 0,\n                                left: size / 4,\n                                opacity: 0.3 + size / 200\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full\",\n                            style: {\n                                width: size / 2,\n                                height: size / 2,\n                                background: color,\n                                top: size / 4,\n                                left: 0,\n                                opacity: 0.3 + size / 200\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full\",\n                            style: {\n                                width: size / 2,\n                                height: size / 2,\n                                background: color,\n                                top: size / 4,\n                                right: 0,\n                                opacity: 0.3 + size / 200\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, undefined);\n            case \"flower\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    style: {\n                        width: size,\n                        height: size\n                    },\n                    children: [\n                        Array.from({\n                            length: 5\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute rounded-full\",\n                                style: {\n                                    width: size / 3,\n                                    height: size / 3,\n                                    background: color,\n                                    top: size / 2 - size / 6,\n                                    left: size / 2 - size / 6,\n                                    transform: \"rotate(\".concat(i * 72, \"deg) translateY(-\").concat(size / 4, \"px)\"),\n                                    opacity: 0.3 + size / 200\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full\",\n                            style: {\n                                width: size / 4,\n                                height: size / 4,\n                                background: element.color === primaryColor ? secondaryColor : primaryColor,\n                                top: size / 2 - size / 8,\n                                left: size / 2 - size / 8,\n                                opacity: 0.6 + size / 200\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, undefined);\n            case \"leaf\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-tl-full rounded-tr-none rounded-bl-none rounded-br-full\",\n                    style: {\n                        width: size * 0.7,\n                        height: size,\n                        background: color,\n                        opacity: 0.2 + size / 200\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined);\n            case \"dot\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full\",\n                    style: {\n                        width: Math.max(4, size / 3),\n                        height: Math.max(4, size / 3),\n                        background: color,\n                        opacity: 0.5 + size / 150\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, undefined);\n            case \"arc\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-tl-full rounded-tr-full rounded-bl-none rounded-br-none border-t-2 border-l-2 border-r-2\",\n                    style: {\n                        width: size,\n                        height: size / 2,\n                        borderColor: color,\n                        opacity: 0.4 + size / 200\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined);\n            case \"circle\":\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full\",\n                    style: {\n                        width: size,\n                        height: size,\n                        background: color,\n                        opacity: 0.2 + size / 200\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"absolute inset-0 w-full h-full overflow-hidden pointer-events-none \".concat(className),\n        style: {\n            zIndex: -1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full relative\",\n            children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute\",\n                    style: {\n                        left: \"\".concat(element.x, \"%\"),\n                        top: \"\".concat(element.y, \"%\"),\n                        transform: \"rotate(\".concat(element.rotate, \"deg)\")\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -15,\n                            0\n                        ],\n                        x: [\n                            0,\n                            element.id % 2 === 0 ? 10 : -10,\n                            0\n                        ],\n                        rotate: [\n                            element.rotate,\n                            element.rotate + 10,\n                            element.rotate\n                        ]\n                    },\n                    transition: {\n                        duration: 5 + Math.random() * 5,\n                        delay: element.delay,\n                        repeat: Infinity,\n                        repeatType: \"reverse\",\n                        ease: \"easeInOut\"\n                    },\n                    children: renderElement(element)\n                }, element.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ParallaxBackground, \"ZW0OdPwGYnHctRvQ4u/lzn19fec=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll\n    ];\n});\n_c = ParallaxBackground;\nvar _c;\n$RefreshReg$(_c, \"ParallaxBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/ParallaxBackground.tsx\n"));

/***/ })

});