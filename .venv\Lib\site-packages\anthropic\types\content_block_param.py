# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .text_block_param import TextBlockParam
from .image_block_param import ImageBlockParam
from .document_block_param import <PERSON>ument<PERSON>lockParam
from .thinking_block_param import Thinking<PERSON><PERSON><PERSON>ara<PERSON>
from .tool_use_block_param import ToolU<PERSON><PERSON><PERSON>Para<PERSON>
from .tool_result_block_param import ToolResultBlockParam
from .redacted_thinking_block_param import RedactedThinkingBlockParam

__all__ = ["ContentBlockParam"]

ContentBlockParam: TypeAlias = Union[
    TextBlockParam,
    ImageBlockParam,
    ToolUseBlockParam,
    ToolResultBlockParam,
    DocumentBlockParam,
    ThinkingBlockParam,
    RedactedThinkingBlockParam,
]
