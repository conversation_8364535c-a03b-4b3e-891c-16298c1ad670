/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gsap";
exports.ids = ["vendor-chunks/gsap"];
exports.modules = {

/***/ "(ssr)/./node_modules/gsap/dist/gsap.js":
/*!****************************************!*\
  !*** ./node_modules/gsap/dist/gsap.js ***!
  \****************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("(function (global, factory) {\n   true ? factory(exports) :\n  0;\n}(this, (function (exports) { 'use strict';\n\n  function _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    subClass.__proto__ = superClass;\n  }\n\n  function _assertThisInitialized(self) {\n    if (self === void 0) {\n      throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n\n    return self;\n  }\n\n  /*!\n   * GSAP 3.13.0\n   * https://gsap.com\n   *\n   * @license Copyright 2008-2025, GreenSock. All rights reserved.\n   * Subject to the terms at https://gsap.com/standard-license\n   * @author: Jack Doyle, <EMAIL>\n  */\n  var _config = {\n    autoSleep: 120,\n    force3D: \"auto\",\n    nullTargetWarn: 1,\n    units: {\n      lineHeight: \"\"\n    }\n  },\n      _defaults = {\n    duration: .5,\n    overwrite: false,\n    delay: 0\n  },\n      _suppressOverwrites,\n      _reverting,\n      _context,\n      _bigNum = 1e8,\n      _tinyNum = 1 / _bigNum,\n      _2PI = Math.PI * 2,\n      _HALF_PI = _2PI / 4,\n      _gsID = 0,\n      _sqrt = Math.sqrt,\n      _cos = Math.cos,\n      _sin = Math.sin,\n      _isString = function _isString(value) {\n    return typeof value === \"string\";\n  },\n      _isFunction = function _isFunction(value) {\n    return typeof value === \"function\";\n  },\n      _isNumber = function _isNumber(value) {\n    return typeof value === \"number\";\n  },\n      _isUndefined = function _isUndefined(value) {\n    return typeof value === \"undefined\";\n  },\n      _isObject = function _isObject(value) {\n    return typeof value === \"object\";\n  },\n      _isNotFalse = function _isNotFalse(value) {\n    return value !== false;\n  },\n      _windowExists = function _windowExists() {\n    return typeof window !== \"undefined\";\n  },\n      _isFuncOrString = function _isFuncOrString(value) {\n    return _isFunction(value) || _isString(value);\n  },\n      _isTypedArray = typeof ArrayBuffer === \"function\" && ArrayBuffer.isView || function () {},\n      _isArray = Array.isArray,\n      _strictNumExp = /(?:-?\\.?\\d|\\.)+/gi,\n      _numExp = /[-+=.]*\\d+[.e\\-+]*\\d*[e\\-+]*\\d*/g,\n      _numWithUnitExp = /[-+=.]*\\d+[.e-]*\\d*[a-z%]*/g,\n      _complexStringNumExp = /[-+=.]*\\d+\\.?\\d*(?:e-|e\\+)?\\d*/gi,\n      _relExp = /[+-]=-?[.\\d]+/,\n      _delimitedValueExp = /[^,'\"\\[\\]\\s]+/gi,\n      _unitExp = /^[+\\-=e\\s\\d]*\\d+[.\\d]*([a-z]*|%)\\s*$/i,\n      _globalTimeline,\n      _win,\n      _coreInitted,\n      _doc,\n      _globals = {},\n      _installScope = {},\n      _coreReady,\n      _install = function _install(scope) {\n    return (_installScope = _merge(scope, _globals)) && gsap;\n  },\n      _missingPlugin = function _missingPlugin(property, value) {\n    return console.warn(\"Invalid property\", property, \"set to\", value, \"Missing plugin? gsap.registerPlugin()\");\n  },\n      _warn = function _warn(message, suppress) {\n    return !suppress && console.warn(message);\n  },\n      _addGlobal = function _addGlobal(name, obj) {\n    return name && (_globals[name] = obj) && _installScope && (_installScope[name] = obj) || _globals;\n  },\n      _emptyFunc = function _emptyFunc() {\n    return 0;\n  },\n      _startAtRevertConfig = {\n    suppressEvents: true,\n    isStart: true,\n    kill: false\n  },\n      _revertConfigNoKill = {\n    suppressEvents: true,\n    kill: false\n  },\n      _revertConfig = {\n    suppressEvents: true\n  },\n      _reservedProps = {},\n      _lazyTweens = [],\n      _lazyLookup = {},\n      _lastRenderedFrame,\n      _plugins = {},\n      _effects = {},\n      _nextGCFrame = 30,\n      _harnessPlugins = [],\n      _callbackNames = \"\",\n      _harness = function _harness(targets) {\n    var target = targets[0],\n        harnessPlugin,\n        i;\n    _isObject(target) || _isFunction(target) || (targets = [targets]);\n\n    if (!(harnessPlugin = (target._gsap || {}).harness)) {\n      i = _harnessPlugins.length;\n\n      while (i-- && !_harnessPlugins[i].targetTest(target)) {}\n\n      harnessPlugin = _harnessPlugins[i];\n    }\n\n    i = targets.length;\n\n    while (i--) {\n      targets[i] && (targets[i]._gsap || (targets[i]._gsap = new GSCache(targets[i], harnessPlugin))) || targets.splice(i, 1);\n    }\n\n    return targets;\n  },\n      _getCache = function _getCache(target) {\n    return target._gsap || _harness(toArray(target))[0]._gsap;\n  },\n      _getProperty = function _getProperty(target, property, v) {\n    return (v = target[property]) && _isFunction(v) ? target[property]() : _isUndefined(v) && target.getAttribute && target.getAttribute(property) || v;\n  },\n      _forEachName = function _forEachName(names, func) {\n    return (names = names.split(\",\")).forEach(func) || names;\n  },\n      _round = function _round(value) {\n    return Math.round(value * 100000) / 100000 || 0;\n  },\n      _roundPrecise = function _roundPrecise(value) {\n    return Math.round(value * 10000000) / 10000000 || 0;\n  },\n      _parseRelative = function _parseRelative(start, value) {\n    var operator = value.charAt(0),\n        end = parseFloat(value.substr(2));\n    start = parseFloat(start);\n    return operator === \"+\" ? start + end : operator === \"-\" ? start - end : operator === \"*\" ? start * end : start / end;\n  },\n      _arrayContainsAny = function _arrayContainsAny(toSearch, toFind) {\n    var l = toFind.length,\n        i = 0;\n\n    for (; toSearch.indexOf(toFind[i]) < 0 && ++i < l;) {}\n\n    return i < l;\n  },\n      _lazyRender = function _lazyRender() {\n    var l = _lazyTweens.length,\n        a = _lazyTweens.slice(0),\n        i,\n        tween;\n\n    _lazyLookup = {};\n    _lazyTweens.length = 0;\n\n    for (i = 0; i < l; i++) {\n      tween = a[i];\n      tween && tween._lazy && (tween.render(tween._lazy[0], tween._lazy[1], true)._lazy = 0);\n    }\n  },\n      _isRevertWorthy = function _isRevertWorthy(animation) {\n    return !!(animation._initted || animation._startAt || animation.add);\n  },\n      _lazySafeRender = function _lazySafeRender(animation, time, suppressEvents, force) {\n    _lazyTweens.length && !_reverting && _lazyRender();\n    animation.render(time, suppressEvents, force || !!(_reverting && time < 0 && _isRevertWorthy(animation)));\n    _lazyTweens.length && !_reverting && _lazyRender();\n  },\n      _numericIfPossible = function _numericIfPossible(value) {\n    var n = parseFloat(value);\n    return (n || n === 0) && (value + \"\").match(_delimitedValueExp).length < 2 ? n : _isString(value) ? value.trim() : value;\n  },\n      _passThrough = function _passThrough(p) {\n    return p;\n  },\n      _setDefaults = function _setDefaults(obj, defaults) {\n    for (var p in defaults) {\n      p in obj || (obj[p] = defaults[p]);\n    }\n\n    return obj;\n  },\n      _setKeyframeDefaults = function _setKeyframeDefaults(excludeDuration) {\n    return function (obj, defaults) {\n      for (var p in defaults) {\n        p in obj || p === \"duration\" && excludeDuration || p === \"ease\" || (obj[p] = defaults[p]);\n      }\n    };\n  },\n      _merge = function _merge(base, toMerge) {\n    for (var p in toMerge) {\n      base[p] = toMerge[p];\n    }\n\n    return base;\n  },\n      _mergeDeep = function _mergeDeep(base, toMerge) {\n    for (var p in toMerge) {\n      p !== \"__proto__\" && p !== \"constructor\" && p !== \"prototype\" && (base[p] = _isObject(toMerge[p]) ? _mergeDeep(base[p] || (base[p] = {}), toMerge[p]) : toMerge[p]);\n    }\n\n    return base;\n  },\n      _copyExcluding = function _copyExcluding(obj, excluding) {\n    var copy = {},\n        p;\n\n    for (p in obj) {\n      p in excluding || (copy[p] = obj[p]);\n    }\n\n    return copy;\n  },\n      _inheritDefaults = function _inheritDefaults(vars) {\n    var parent = vars.parent || _globalTimeline,\n        func = vars.keyframes ? _setKeyframeDefaults(_isArray(vars.keyframes)) : _setDefaults;\n\n    if (_isNotFalse(vars.inherit)) {\n      while (parent) {\n        func(vars, parent.vars.defaults);\n        parent = parent.parent || parent._dp;\n      }\n    }\n\n    return vars;\n  },\n      _arraysMatch = function _arraysMatch(a1, a2) {\n    var i = a1.length,\n        match = i === a2.length;\n\n    while (match && i-- && a1[i] === a2[i]) {}\n\n    return i < 0;\n  },\n      _addLinkedListItem = function _addLinkedListItem(parent, child, firstProp, lastProp, sortBy) {\n    if (firstProp === void 0) {\n      firstProp = \"_first\";\n    }\n\n    if (lastProp === void 0) {\n      lastProp = \"_last\";\n    }\n\n    var prev = parent[lastProp],\n        t;\n\n    if (sortBy) {\n      t = child[sortBy];\n\n      while (prev && prev[sortBy] > t) {\n        prev = prev._prev;\n      }\n    }\n\n    if (prev) {\n      child._next = prev._next;\n      prev._next = child;\n    } else {\n      child._next = parent[firstProp];\n      parent[firstProp] = child;\n    }\n\n    if (child._next) {\n      child._next._prev = child;\n    } else {\n      parent[lastProp] = child;\n    }\n\n    child._prev = prev;\n    child.parent = child._dp = parent;\n    return child;\n  },\n      _removeLinkedListItem = function _removeLinkedListItem(parent, child, firstProp, lastProp) {\n    if (firstProp === void 0) {\n      firstProp = \"_first\";\n    }\n\n    if (lastProp === void 0) {\n      lastProp = \"_last\";\n    }\n\n    var prev = child._prev,\n        next = child._next;\n\n    if (prev) {\n      prev._next = next;\n    } else if (parent[firstProp] === child) {\n      parent[firstProp] = next;\n    }\n\n    if (next) {\n      next._prev = prev;\n    } else if (parent[lastProp] === child) {\n      parent[lastProp] = prev;\n    }\n\n    child._next = child._prev = child.parent = null;\n  },\n      _removeFromParent = function _removeFromParent(child, onlyIfParentHasAutoRemove) {\n    child.parent && (!onlyIfParentHasAutoRemove || child.parent.autoRemoveChildren) && child.parent.remove && child.parent.remove(child);\n    child._act = 0;\n  },\n      _uncache = function _uncache(animation, child) {\n    if (animation && (!child || child._end > animation._dur || child._start < 0)) {\n      var a = animation;\n\n      while (a) {\n        a._dirty = 1;\n        a = a.parent;\n      }\n    }\n\n    return animation;\n  },\n      _recacheAncestors = function _recacheAncestors(animation) {\n    var parent = animation.parent;\n\n    while (parent && parent.parent) {\n      parent._dirty = 1;\n      parent.totalDuration();\n      parent = parent.parent;\n    }\n\n    return animation;\n  },\n      _rewindStartAt = function _rewindStartAt(tween, totalTime, suppressEvents, force) {\n    return tween._startAt && (_reverting ? tween._startAt.revert(_revertConfigNoKill) : tween.vars.immediateRender && !tween.vars.autoRevert || tween._startAt.render(totalTime, true, force));\n  },\n      _hasNoPausedAncestors = function _hasNoPausedAncestors(animation) {\n    return !animation || animation._ts && _hasNoPausedAncestors(animation.parent);\n  },\n      _elapsedCycleDuration = function _elapsedCycleDuration(animation) {\n    return animation._repeat ? _animationCycle(animation._tTime, animation = animation.duration() + animation._rDelay) * animation : 0;\n  },\n      _animationCycle = function _animationCycle(tTime, cycleDuration) {\n    var whole = Math.floor(tTime = _roundPrecise(tTime / cycleDuration));\n    return tTime && whole === tTime ? whole - 1 : whole;\n  },\n      _parentToChildTotalTime = function _parentToChildTotalTime(parentTime, child) {\n    return (parentTime - child._start) * child._ts + (child._ts >= 0 ? 0 : child._dirty ? child.totalDuration() : child._tDur);\n  },\n      _setEnd = function _setEnd(animation) {\n    return animation._end = _roundPrecise(animation._start + (animation._tDur / Math.abs(animation._ts || animation._rts || _tinyNum) || 0));\n  },\n      _alignPlayhead = function _alignPlayhead(animation, totalTime) {\n    var parent = animation._dp;\n\n    if (parent && parent.smoothChildTiming && animation._ts) {\n      animation._start = _roundPrecise(parent._time - (animation._ts > 0 ? totalTime / animation._ts : ((animation._dirty ? animation.totalDuration() : animation._tDur) - totalTime) / -animation._ts));\n\n      _setEnd(animation);\n\n      parent._dirty || _uncache(parent, animation);\n    }\n\n    return animation;\n  },\n      _postAddChecks = function _postAddChecks(timeline, child) {\n    var t;\n\n    if (child._time || !child._dur && child._initted || child._start < timeline._time && (child._dur || !child.add)) {\n      t = _parentToChildTotalTime(timeline.rawTime(), child);\n\n      if (!child._dur || _clamp(0, child.totalDuration(), t) - child._tTime > _tinyNum) {\n        child.render(t, true);\n      }\n    }\n\n    if (_uncache(timeline, child)._dp && timeline._initted && timeline._time >= timeline._dur && timeline._ts) {\n      if (timeline._dur < timeline.duration()) {\n        t = timeline;\n\n        while (t._dp) {\n          t.rawTime() >= 0 && t.totalTime(t._tTime);\n          t = t._dp;\n        }\n      }\n\n      timeline._zTime = -_tinyNum;\n    }\n  },\n      _addToTimeline = function _addToTimeline(timeline, child, position, skipChecks) {\n    child.parent && _removeFromParent(child);\n    child._start = _roundPrecise((_isNumber(position) ? position : position || timeline !== _globalTimeline ? _parsePosition(timeline, position, child) : timeline._time) + child._delay);\n    child._end = _roundPrecise(child._start + (child.totalDuration() / Math.abs(child.timeScale()) || 0));\n\n    _addLinkedListItem(timeline, child, \"_first\", \"_last\", timeline._sort ? \"_start\" : 0);\n\n    _isFromOrFromStart(child) || (timeline._recent = child);\n    skipChecks || _postAddChecks(timeline, child);\n    timeline._ts < 0 && _alignPlayhead(timeline, timeline._tTime);\n    return timeline;\n  },\n      _scrollTrigger = function _scrollTrigger(animation, trigger) {\n    return (_globals.ScrollTrigger || _missingPlugin(\"scrollTrigger\", trigger)) && _globals.ScrollTrigger.create(trigger, animation);\n  },\n      _attemptInitTween = function _attemptInitTween(tween, time, force, suppressEvents, tTime) {\n    _initTween(tween, time, tTime);\n\n    if (!tween._initted) {\n      return 1;\n    }\n\n    if (!force && tween._pt && !_reverting && (tween._dur && tween.vars.lazy !== false || !tween._dur && tween.vars.lazy) && _lastRenderedFrame !== _ticker.frame) {\n      _lazyTweens.push(tween);\n\n      tween._lazy = [tTime, suppressEvents];\n      return 1;\n    }\n  },\n      _parentPlayheadIsBeforeStart = function _parentPlayheadIsBeforeStart(_ref) {\n    var parent = _ref.parent;\n    return parent && parent._ts && parent._initted && !parent._lock && (parent.rawTime() < 0 || _parentPlayheadIsBeforeStart(parent));\n  },\n      _isFromOrFromStart = function _isFromOrFromStart(_ref2) {\n    var data = _ref2.data;\n    return data === \"isFromStart\" || data === \"isStart\";\n  },\n      _renderZeroDurationTween = function _renderZeroDurationTween(tween, totalTime, suppressEvents, force) {\n    var prevRatio = tween.ratio,\n        ratio = totalTime < 0 || !totalTime && (!tween._start && _parentPlayheadIsBeforeStart(tween) && !(!tween._initted && _isFromOrFromStart(tween)) || (tween._ts < 0 || tween._dp._ts < 0) && !_isFromOrFromStart(tween)) ? 0 : 1,\n        repeatDelay = tween._rDelay,\n        tTime = 0,\n        pt,\n        iteration,\n        prevIteration;\n\n    if (repeatDelay && tween._repeat) {\n      tTime = _clamp(0, tween._tDur, totalTime);\n      iteration = _animationCycle(tTime, repeatDelay);\n      tween._yoyo && iteration & 1 && (ratio = 1 - ratio);\n\n      if (iteration !== _animationCycle(tween._tTime, repeatDelay)) {\n        prevRatio = 1 - ratio;\n        tween.vars.repeatRefresh && tween._initted && tween.invalidate();\n      }\n    }\n\n    if (ratio !== prevRatio || _reverting || force || tween._zTime === _tinyNum || !totalTime && tween._zTime) {\n      if (!tween._initted && _attemptInitTween(tween, totalTime, force, suppressEvents, tTime)) {\n        return;\n      }\n\n      prevIteration = tween._zTime;\n      tween._zTime = totalTime || (suppressEvents ? _tinyNum : 0);\n      suppressEvents || (suppressEvents = totalTime && !prevIteration);\n      tween.ratio = ratio;\n      tween._from && (ratio = 1 - ratio);\n      tween._time = 0;\n      tween._tTime = tTime;\n      pt = tween._pt;\n\n      while (pt) {\n        pt.r(ratio, pt.d);\n        pt = pt._next;\n      }\n\n      totalTime < 0 && _rewindStartAt(tween, totalTime, suppressEvents, true);\n      tween._onUpdate && !suppressEvents && _callback(tween, \"onUpdate\");\n      tTime && tween._repeat && !suppressEvents && tween.parent && _callback(tween, \"onRepeat\");\n\n      if ((totalTime >= tween._tDur || totalTime < 0) && tween.ratio === ratio) {\n        ratio && _removeFromParent(tween, 1);\n\n        if (!suppressEvents && !_reverting) {\n          _callback(tween, ratio ? \"onComplete\" : \"onReverseComplete\", true);\n\n          tween._prom && tween._prom();\n        }\n      }\n    } else if (!tween._zTime) {\n      tween._zTime = totalTime;\n    }\n  },\n      _findNextPauseTween = function _findNextPauseTween(animation, prevTime, time) {\n    var child;\n\n    if (time > prevTime) {\n      child = animation._first;\n\n      while (child && child._start <= time) {\n        if (child.data === \"isPause\" && child._start > prevTime) {\n          return child;\n        }\n\n        child = child._next;\n      }\n    } else {\n      child = animation._last;\n\n      while (child && child._start >= time) {\n        if (child.data === \"isPause\" && child._start < prevTime) {\n          return child;\n        }\n\n        child = child._prev;\n      }\n    }\n  },\n      _setDuration = function _setDuration(animation, duration, skipUncache, leavePlayhead) {\n    var repeat = animation._repeat,\n        dur = _roundPrecise(duration) || 0,\n        totalProgress = animation._tTime / animation._tDur;\n    totalProgress && !leavePlayhead && (animation._time *= dur / animation._dur);\n    animation._dur = dur;\n    animation._tDur = !repeat ? dur : repeat < 0 ? 1e10 : _roundPrecise(dur * (repeat + 1) + animation._rDelay * repeat);\n    totalProgress > 0 && !leavePlayhead && _alignPlayhead(animation, animation._tTime = animation._tDur * totalProgress);\n    animation.parent && _setEnd(animation);\n    skipUncache || _uncache(animation.parent, animation);\n    return animation;\n  },\n      _onUpdateTotalDuration = function _onUpdateTotalDuration(animation) {\n    return animation instanceof Timeline ? _uncache(animation) : _setDuration(animation, animation._dur);\n  },\n      _zeroPosition = {\n    _start: 0,\n    endTime: _emptyFunc,\n    totalDuration: _emptyFunc\n  },\n      _parsePosition = function _parsePosition(animation, position, percentAnimation) {\n    var labels = animation.labels,\n        recent = animation._recent || _zeroPosition,\n        clippedDuration = animation.duration() >= _bigNum ? recent.endTime(false) : animation._dur,\n        i,\n        offset,\n        isPercent;\n\n    if (_isString(position) && (isNaN(position) || position in labels)) {\n      offset = position.charAt(0);\n      isPercent = position.substr(-1) === \"%\";\n      i = position.indexOf(\"=\");\n\n      if (offset === \"<\" || offset === \">\") {\n        i >= 0 && (position = position.replace(/=/, \"\"));\n        return (offset === \"<\" ? recent._start : recent.endTime(recent._repeat >= 0)) + (parseFloat(position.substr(1)) || 0) * (isPercent ? (i < 0 ? recent : percentAnimation).totalDuration() / 100 : 1);\n      }\n\n      if (i < 0) {\n        position in labels || (labels[position] = clippedDuration);\n        return labels[position];\n      }\n\n      offset = parseFloat(position.charAt(i - 1) + position.substr(i + 1));\n\n      if (isPercent && percentAnimation) {\n        offset = offset / 100 * (_isArray(percentAnimation) ? percentAnimation[0] : percentAnimation).totalDuration();\n      }\n\n      return i > 1 ? _parsePosition(animation, position.substr(0, i - 1), percentAnimation) + offset : clippedDuration + offset;\n    }\n\n    return position == null ? clippedDuration : +position;\n  },\n      _createTweenType = function _createTweenType(type, params, timeline) {\n    var isLegacy = _isNumber(params[1]),\n        varsIndex = (isLegacy ? 2 : 1) + (type < 2 ? 0 : 1),\n        vars = params[varsIndex],\n        irVars,\n        parent;\n\n    isLegacy && (vars.duration = params[1]);\n    vars.parent = timeline;\n\n    if (type) {\n      irVars = vars;\n      parent = timeline;\n\n      while (parent && !(\"immediateRender\" in irVars)) {\n        irVars = parent.vars.defaults || {};\n        parent = _isNotFalse(parent.vars.inherit) && parent.parent;\n      }\n\n      vars.immediateRender = _isNotFalse(irVars.immediateRender);\n      type < 2 ? vars.runBackwards = 1 : vars.startAt = params[varsIndex - 1];\n    }\n\n    return new Tween(params[0], vars, params[varsIndex + 1]);\n  },\n      _conditionalReturn = function _conditionalReturn(value, func) {\n    return value || value === 0 ? func(value) : func;\n  },\n      _clamp = function _clamp(min, max, value) {\n    return value < min ? min : value > max ? max : value;\n  },\n      getUnit = function getUnit(value, v) {\n    return !_isString(value) || !(v = _unitExp.exec(value)) ? \"\" : v[1];\n  },\n      clamp = function clamp(min, max, value) {\n    return _conditionalReturn(value, function (v) {\n      return _clamp(min, max, v);\n    });\n  },\n      _slice = [].slice,\n      _isArrayLike = function _isArrayLike(value, nonEmpty) {\n    return value && _isObject(value) && \"length\" in value && (!nonEmpty && !value.length || value.length - 1 in value && _isObject(value[0])) && !value.nodeType && value !== _win;\n  },\n      _flatten = function _flatten(ar, leaveStrings, accumulator) {\n    if (accumulator === void 0) {\n      accumulator = [];\n    }\n\n    return ar.forEach(function (value) {\n      var _accumulator;\n\n      return _isString(value) && !leaveStrings || _isArrayLike(value, 1) ? (_accumulator = accumulator).push.apply(_accumulator, toArray(value)) : accumulator.push(value);\n    }) || accumulator;\n  },\n      toArray = function toArray(value, scope, leaveStrings) {\n    return _context && !scope && _context.selector ? _context.selector(value) : _isString(value) && !leaveStrings && (_coreInitted || !_wake()) ? _slice.call((scope || _doc).querySelectorAll(value), 0) : _isArray(value) ? _flatten(value, leaveStrings) : _isArrayLike(value) ? _slice.call(value, 0) : value ? [value] : [];\n  },\n      selector = function selector(value) {\n    value = toArray(value)[0] || _warn(\"Invalid scope\") || {};\n    return function (v) {\n      var el = value.current || value.nativeElement || value;\n      return toArray(v, el.querySelectorAll ? el : el === value ? _warn(\"Invalid scope\") || _doc.createElement(\"div\") : value);\n    };\n  },\n      shuffle = function shuffle(a) {\n    return a.sort(function () {\n      return .5 - Math.random();\n    });\n  },\n      distribute = function distribute(v) {\n    if (_isFunction(v)) {\n      return v;\n    }\n\n    var vars = _isObject(v) ? v : {\n      each: v\n    },\n        ease = _parseEase(vars.ease),\n        from = vars.from || 0,\n        base = parseFloat(vars.base) || 0,\n        cache = {},\n        isDecimal = from > 0 && from < 1,\n        ratios = isNaN(from) || isDecimal,\n        axis = vars.axis,\n        ratioX = from,\n        ratioY = from;\n\n    if (_isString(from)) {\n      ratioX = ratioY = {\n        center: .5,\n        edges: .5,\n        end: 1\n      }[from] || 0;\n    } else if (!isDecimal && ratios) {\n      ratioX = from[0];\n      ratioY = from[1];\n    }\n\n    return function (i, target, a) {\n      var l = (a || vars).length,\n          distances = cache[l],\n          originX,\n          originY,\n          x,\n          y,\n          d,\n          j,\n          max,\n          min,\n          wrapAt;\n\n      if (!distances) {\n        wrapAt = vars.grid === \"auto\" ? 0 : (vars.grid || [1, _bigNum])[1];\n\n        if (!wrapAt) {\n          max = -_bigNum;\n\n          while (max < (max = a[wrapAt++].getBoundingClientRect().left) && wrapAt < l) {}\n\n          wrapAt < l && wrapAt--;\n        }\n\n        distances = cache[l] = [];\n        originX = ratios ? Math.min(wrapAt, l) * ratioX - .5 : from % wrapAt;\n        originY = wrapAt === _bigNum ? 0 : ratios ? l * ratioY / wrapAt - .5 : from / wrapAt | 0;\n        max = 0;\n        min = _bigNum;\n\n        for (j = 0; j < l; j++) {\n          x = j % wrapAt - originX;\n          y = originY - (j / wrapAt | 0);\n          distances[j] = d = !axis ? _sqrt(x * x + y * y) : Math.abs(axis === \"y\" ? y : x);\n          d > max && (max = d);\n          d < min && (min = d);\n        }\n\n        from === \"random\" && shuffle(distances);\n        distances.max = max - min;\n        distances.min = min;\n        distances.v = l = (parseFloat(vars.amount) || parseFloat(vars.each) * (wrapAt > l ? l - 1 : !axis ? Math.max(wrapAt, l / wrapAt) : axis === \"y\" ? l / wrapAt : wrapAt) || 0) * (from === \"edges\" ? -1 : 1);\n        distances.b = l < 0 ? base - l : base;\n        distances.u = getUnit(vars.amount || vars.each) || 0;\n        ease = ease && l < 0 ? _invertEase(ease) : ease;\n      }\n\n      l = (distances[i] - distances.min) / distances.max || 0;\n      return _roundPrecise(distances.b + (ease ? ease(l) : l) * distances.v) + distances.u;\n    };\n  },\n      _roundModifier = function _roundModifier(v) {\n    var p = Math.pow(10, ((v + \"\").split(\".\")[1] || \"\").length);\n    return function (raw) {\n      var n = _roundPrecise(Math.round(parseFloat(raw) / v) * v * p);\n\n      return (n - n % 1) / p + (_isNumber(raw) ? 0 : getUnit(raw));\n    };\n  },\n      snap = function snap(snapTo, value) {\n    var isArray = _isArray(snapTo),\n        radius,\n        is2D;\n\n    if (!isArray && _isObject(snapTo)) {\n      radius = isArray = snapTo.radius || _bigNum;\n\n      if (snapTo.values) {\n        snapTo = toArray(snapTo.values);\n\n        if (is2D = !_isNumber(snapTo[0])) {\n          radius *= radius;\n        }\n      } else {\n        snapTo = _roundModifier(snapTo.increment);\n      }\n    }\n\n    return _conditionalReturn(value, !isArray ? _roundModifier(snapTo) : _isFunction(snapTo) ? function (raw) {\n      is2D = snapTo(raw);\n      return Math.abs(is2D - raw) <= radius ? is2D : raw;\n    } : function (raw) {\n      var x = parseFloat(is2D ? raw.x : raw),\n          y = parseFloat(is2D ? raw.y : 0),\n          min = _bigNum,\n          closest = 0,\n          i = snapTo.length,\n          dx,\n          dy;\n\n      while (i--) {\n        if (is2D) {\n          dx = snapTo[i].x - x;\n          dy = snapTo[i].y - y;\n          dx = dx * dx + dy * dy;\n        } else {\n          dx = Math.abs(snapTo[i] - x);\n        }\n\n        if (dx < min) {\n          min = dx;\n          closest = i;\n        }\n      }\n\n      closest = !radius || min <= radius ? snapTo[closest] : raw;\n      return is2D || closest === raw || _isNumber(raw) ? closest : closest + getUnit(raw);\n    });\n  },\n      random = function random(min, max, roundingIncrement, returnFunction) {\n    return _conditionalReturn(_isArray(min) ? !max : roundingIncrement === true ? !!(roundingIncrement = 0) : !returnFunction, function () {\n      return _isArray(min) ? min[~~(Math.random() * min.length)] : (roundingIncrement = roundingIncrement || 1e-5) && (returnFunction = roundingIncrement < 1 ? Math.pow(10, (roundingIncrement + \"\").length - 2) : 1) && Math.floor(Math.round((min - roundingIncrement / 2 + Math.random() * (max - min + roundingIncrement * .99)) / roundingIncrement) * roundingIncrement * returnFunction) / returnFunction;\n    });\n  },\n      pipe = function pipe() {\n    for (var _len = arguments.length, functions = new Array(_len), _key = 0; _key < _len; _key++) {\n      functions[_key] = arguments[_key];\n    }\n\n    return function (value) {\n      return functions.reduce(function (v, f) {\n        return f(v);\n      }, value);\n    };\n  },\n      unitize = function unitize(func, unit) {\n    return function (value) {\n      return func(parseFloat(value)) + (unit || getUnit(value));\n    };\n  },\n      normalize = function normalize(min, max, value) {\n    return mapRange(min, max, 0, 1, value);\n  },\n      _wrapArray = function _wrapArray(a, wrapper, value) {\n    return _conditionalReturn(value, function (index) {\n      return a[~~wrapper(index)];\n    });\n  },\n      wrap = function wrap(min, max, value) {\n    var range = max - min;\n    return _isArray(min) ? _wrapArray(min, wrap(0, min.length), max) : _conditionalReturn(value, function (value) {\n      return (range + (value - min) % range) % range + min;\n    });\n  },\n      wrapYoyo = function wrapYoyo(min, max, value) {\n    var range = max - min,\n        total = range * 2;\n    return _isArray(min) ? _wrapArray(min, wrapYoyo(0, min.length - 1), max) : _conditionalReturn(value, function (value) {\n      value = (total + (value - min) % total) % total || 0;\n      return min + (value > range ? total - value : value);\n    });\n  },\n      _replaceRandom = function _replaceRandom(value) {\n    var prev = 0,\n        s = \"\",\n        i,\n        nums,\n        end,\n        isArray;\n\n    while (~(i = value.indexOf(\"random(\", prev))) {\n      end = value.indexOf(\")\", i);\n      isArray = value.charAt(i + 7) === \"[\";\n      nums = value.substr(i + 7, end - i - 7).match(isArray ? _delimitedValueExp : _strictNumExp);\n      s += value.substr(prev, i - prev) + random(isArray ? nums : +nums[0], isArray ? 0 : +nums[1], +nums[2] || 1e-5);\n      prev = end + 1;\n    }\n\n    return s + value.substr(prev, value.length - prev);\n  },\n      mapRange = function mapRange(inMin, inMax, outMin, outMax, value) {\n    var inRange = inMax - inMin,\n        outRange = outMax - outMin;\n    return _conditionalReturn(value, function (value) {\n      return outMin + ((value - inMin) / inRange * outRange || 0);\n    });\n  },\n      interpolate = function interpolate(start, end, progress, mutate) {\n    var func = isNaN(start + end) ? 0 : function (p) {\n      return (1 - p) * start + p * end;\n    };\n\n    if (!func) {\n      var isString = _isString(start),\n          master = {},\n          p,\n          i,\n          interpolators,\n          l,\n          il;\n\n      progress === true && (mutate = 1) && (progress = null);\n\n      if (isString) {\n        start = {\n          p: start\n        };\n        end = {\n          p: end\n        };\n      } else if (_isArray(start) && !_isArray(end)) {\n        interpolators = [];\n        l = start.length;\n        il = l - 2;\n\n        for (i = 1; i < l; i++) {\n          interpolators.push(interpolate(start[i - 1], start[i]));\n        }\n\n        l--;\n\n        func = function func(p) {\n          p *= l;\n          var i = Math.min(il, ~~p);\n          return interpolators[i](p - i);\n        };\n\n        progress = end;\n      } else if (!mutate) {\n        start = _merge(_isArray(start) ? [] : {}, start);\n      }\n\n      if (!interpolators) {\n        for (p in end) {\n          _addPropTween.call(master, start, p, \"get\", end[p]);\n        }\n\n        func = function func(p) {\n          return _renderPropTweens(p, master) || (isString ? start.p : start);\n        };\n      }\n    }\n\n    return _conditionalReturn(progress, func);\n  },\n      _getLabelInDirection = function _getLabelInDirection(timeline, fromTime, backward) {\n    var labels = timeline.labels,\n        min = _bigNum,\n        p,\n        distance,\n        label;\n\n    for (p in labels) {\n      distance = labels[p] - fromTime;\n\n      if (distance < 0 === !!backward && distance && min > (distance = Math.abs(distance))) {\n        label = p;\n        min = distance;\n      }\n    }\n\n    return label;\n  },\n      _callback = function _callback(animation, type, executeLazyFirst) {\n    var v = animation.vars,\n        callback = v[type],\n        prevContext = _context,\n        context = animation._ctx,\n        params,\n        scope,\n        result;\n\n    if (!callback) {\n      return;\n    }\n\n    params = v[type + \"Params\"];\n    scope = v.callbackScope || animation;\n    executeLazyFirst && _lazyTweens.length && _lazyRender();\n    context && (_context = context);\n    result = params ? callback.apply(scope, params) : callback.call(scope);\n    _context = prevContext;\n    return result;\n  },\n      _interrupt = function _interrupt(animation) {\n    _removeFromParent(animation);\n\n    animation.scrollTrigger && animation.scrollTrigger.kill(!!_reverting);\n    animation.progress() < 1 && _callback(animation, \"onInterrupt\");\n    return animation;\n  },\n      _quickTween,\n      _registerPluginQueue = [],\n      _createPlugin = function _createPlugin(config) {\n    if (!config) return;\n    config = !config.name && config[\"default\"] || config;\n\n    if (_windowExists() || config.headless) {\n      var name = config.name,\n          isFunc = _isFunction(config),\n          Plugin = name && !isFunc && config.init ? function () {\n        this._props = [];\n      } : config,\n          instanceDefaults = {\n        init: _emptyFunc,\n        render: _renderPropTweens,\n        add: _addPropTween,\n        kill: _killPropTweensOf,\n        modifier: _addPluginModifier,\n        rawVars: 0\n      },\n          statics = {\n        targetTest: 0,\n        get: 0,\n        getSetter: _getSetter,\n        aliases: {},\n        register: 0\n      };\n\n      _wake();\n\n      if (config !== Plugin) {\n        if (_plugins[name]) {\n          return;\n        }\n\n        _setDefaults(Plugin, _setDefaults(_copyExcluding(config, instanceDefaults), statics));\n\n        _merge(Plugin.prototype, _merge(instanceDefaults, _copyExcluding(config, statics)));\n\n        _plugins[Plugin.prop = name] = Plugin;\n\n        if (config.targetTest) {\n          _harnessPlugins.push(Plugin);\n\n          _reservedProps[name] = 1;\n        }\n\n        name = (name === \"css\" ? \"CSS\" : name.charAt(0).toUpperCase() + name.substr(1)) + \"Plugin\";\n      }\n\n      _addGlobal(name, Plugin);\n\n      config.register && config.register(gsap, Plugin, PropTween);\n    } else {\n      _registerPluginQueue.push(config);\n    }\n  },\n      _255 = 255,\n      _colorLookup = {\n    aqua: [0, _255, _255],\n    lime: [0, _255, 0],\n    silver: [192, 192, 192],\n    black: [0, 0, 0],\n    maroon: [128, 0, 0],\n    teal: [0, 128, 128],\n    blue: [0, 0, _255],\n    navy: [0, 0, 128],\n    white: [_255, _255, _255],\n    olive: [128, 128, 0],\n    yellow: [_255, _255, 0],\n    orange: [_255, 165, 0],\n    gray: [128, 128, 128],\n    purple: [128, 0, 128],\n    green: [0, 128, 0],\n    red: [_255, 0, 0],\n    pink: [_255, 192, 203],\n    cyan: [0, _255, _255],\n    transparent: [_255, _255, _255, 0]\n  },\n      _hue = function _hue(h, m1, m2) {\n    h += h < 0 ? 1 : h > 1 ? -1 : 0;\n    return (h * 6 < 1 ? m1 + (m2 - m1) * h * 6 : h < .5 ? m2 : h * 3 < 2 ? m1 + (m2 - m1) * (2 / 3 - h) * 6 : m1) * _255 + .5 | 0;\n  },\n      splitColor = function splitColor(v, toHSL, forceAlpha) {\n    var a = !v ? _colorLookup.black : _isNumber(v) ? [v >> 16, v >> 8 & _255, v & _255] : 0,\n        r,\n        g,\n        b,\n        h,\n        s,\n        l,\n        max,\n        min,\n        d,\n        wasHSL;\n\n    if (!a) {\n      if (v.substr(-1) === \",\") {\n        v = v.substr(0, v.length - 1);\n      }\n\n      if (_colorLookup[v]) {\n        a = _colorLookup[v];\n      } else if (v.charAt(0) === \"#\") {\n        if (v.length < 6) {\n          r = v.charAt(1);\n          g = v.charAt(2);\n          b = v.charAt(3);\n          v = \"#\" + r + r + g + g + b + b + (v.length === 5 ? v.charAt(4) + v.charAt(4) : \"\");\n        }\n\n        if (v.length === 9) {\n          a = parseInt(v.substr(1, 6), 16);\n          return [a >> 16, a >> 8 & _255, a & _255, parseInt(v.substr(7), 16) / 255];\n        }\n\n        v = parseInt(v.substr(1), 16);\n        a = [v >> 16, v >> 8 & _255, v & _255];\n      } else if (v.substr(0, 3) === \"hsl\") {\n        a = wasHSL = v.match(_strictNumExp);\n\n        if (!toHSL) {\n          h = +a[0] % 360 / 360;\n          s = +a[1] / 100;\n          l = +a[2] / 100;\n          g = l <= .5 ? l * (s + 1) : l + s - l * s;\n          r = l * 2 - g;\n          a.length > 3 && (a[3] *= 1);\n          a[0] = _hue(h + 1 / 3, r, g);\n          a[1] = _hue(h, r, g);\n          a[2] = _hue(h - 1 / 3, r, g);\n        } else if (~v.indexOf(\"=\")) {\n          a = v.match(_numExp);\n          forceAlpha && a.length < 4 && (a[3] = 1);\n          return a;\n        }\n      } else {\n        a = v.match(_strictNumExp) || _colorLookup.transparent;\n      }\n\n      a = a.map(Number);\n    }\n\n    if (toHSL && !wasHSL) {\n      r = a[0] / _255;\n      g = a[1] / _255;\n      b = a[2] / _255;\n      max = Math.max(r, g, b);\n      min = Math.min(r, g, b);\n      l = (max + min) / 2;\n\n      if (max === min) {\n        h = s = 0;\n      } else {\n        d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        h = max === r ? (g - b) / d + (g < b ? 6 : 0) : max === g ? (b - r) / d + 2 : (r - g) / d + 4;\n        h *= 60;\n      }\n\n      a[0] = ~~(h + .5);\n      a[1] = ~~(s * 100 + .5);\n      a[2] = ~~(l * 100 + .5);\n    }\n\n    forceAlpha && a.length < 4 && (a[3] = 1);\n    return a;\n  },\n      _colorOrderData = function _colorOrderData(v) {\n    var values = [],\n        c = [],\n        i = -1;\n    v.split(_colorExp).forEach(function (v) {\n      var a = v.match(_numWithUnitExp) || [];\n      values.push.apply(values, a);\n      c.push(i += a.length + 1);\n    });\n    values.c = c;\n    return values;\n  },\n      _formatColors = function _formatColors(s, toHSL, orderMatchData) {\n    var result = \"\",\n        colors = (s + result).match(_colorExp),\n        type = toHSL ? \"hsla(\" : \"rgba(\",\n        i = 0,\n        c,\n        shell,\n        d,\n        l;\n\n    if (!colors) {\n      return s;\n    }\n\n    colors = colors.map(function (color) {\n      return (color = splitColor(color, toHSL, 1)) && type + (toHSL ? color[0] + \",\" + color[1] + \"%,\" + color[2] + \"%,\" + color[3] : color.join(\",\")) + \")\";\n    });\n\n    if (orderMatchData) {\n      d = _colorOrderData(s);\n      c = orderMatchData.c;\n\n      if (c.join(result) !== d.c.join(result)) {\n        shell = s.replace(_colorExp, \"1\").split(_numWithUnitExp);\n        l = shell.length - 1;\n\n        for (; i < l; i++) {\n          result += shell[i] + (~c.indexOf(i) ? colors.shift() || type + \"0,0,0,0)\" : (d.length ? d : colors.length ? colors : orderMatchData).shift());\n        }\n      }\n    }\n\n    if (!shell) {\n      shell = s.split(_colorExp);\n      l = shell.length - 1;\n\n      for (; i < l; i++) {\n        result += shell[i] + colors[i];\n      }\n    }\n\n    return result + shell[l];\n  },\n      _colorExp = function () {\n    var s = \"(?:\\\\b(?:(?:rgb|rgba|hsl|hsla)\\\\(.+?\\\\))|\\\\B#(?:[0-9a-f]{3,4}){1,2}\\\\b\",\n        p;\n\n    for (p in _colorLookup) {\n      s += \"|\" + p + \"\\\\b\";\n    }\n\n    return new RegExp(s + \")\", \"gi\");\n  }(),\n      _hslExp = /hsl[a]?\\(/,\n      _colorStringFilter = function _colorStringFilter(a) {\n    var combined = a.join(\" \"),\n        toHSL;\n    _colorExp.lastIndex = 0;\n\n    if (_colorExp.test(combined)) {\n      toHSL = _hslExp.test(combined);\n      a[1] = _formatColors(a[1], toHSL);\n      a[0] = _formatColors(a[0], toHSL, _colorOrderData(a[1]));\n      return true;\n    }\n  },\n      _tickerActive,\n      _ticker = function () {\n    var _getTime = Date.now,\n        _lagThreshold = 500,\n        _adjustedLag = 33,\n        _startTime = _getTime(),\n        _lastUpdate = _startTime,\n        _gap = 1000 / 240,\n        _nextTime = _gap,\n        _listeners = [],\n        _id,\n        _req,\n        _raf,\n        _self,\n        _delta,\n        _i,\n        _tick = function _tick(v) {\n      var elapsed = _getTime() - _lastUpdate,\n          manual = v === true,\n          overlap,\n          dispatch,\n          time,\n          frame;\n\n      (elapsed > _lagThreshold || elapsed < 0) && (_startTime += elapsed - _adjustedLag);\n      _lastUpdate += elapsed;\n      time = _lastUpdate - _startTime;\n      overlap = time - _nextTime;\n\n      if (overlap > 0 || manual) {\n        frame = ++_self.frame;\n        _delta = time - _self.time * 1000;\n        _self.time = time = time / 1000;\n        _nextTime += overlap + (overlap >= _gap ? 4 : _gap - overlap);\n        dispatch = 1;\n      }\n\n      manual || (_id = _req(_tick));\n\n      if (dispatch) {\n        for (_i = 0; _i < _listeners.length; _i++) {\n          _listeners[_i](time, _delta, frame, v);\n        }\n      }\n    };\n\n    _self = {\n      time: 0,\n      frame: 0,\n      tick: function tick() {\n        _tick(true);\n      },\n      deltaRatio: function deltaRatio(fps) {\n        return _delta / (1000 / (fps || 60));\n      },\n      wake: function wake() {\n        if (_coreReady) {\n          if (!_coreInitted && _windowExists()) {\n            _win = _coreInitted = window;\n            _doc = _win.document || {};\n            _globals.gsap = gsap;\n            (_win.gsapVersions || (_win.gsapVersions = [])).push(gsap.version);\n\n            _install(_installScope || _win.GreenSockGlobals || !_win.gsap && _win || {});\n\n            _registerPluginQueue.forEach(_createPlugin);\n          }\n\n          _raf = typeof requestAnimationFrame !== \"undefined\" && requestAnimationFrame;\n          _id && _self.sleep();\n\n          _req = _raf || function (f) {\n            return setTimeout(f, _nextTime - _self.time * 1000 + 1 | 0);\n          };\n\n          _tickerActive = 1;\n\n          _tick(2);\n        }\n      },\n      sleep: function sleep() {\n        (_raf ? cancelAnimationFrame : clearTimeout)(_id);\n        _tickerActive = 0;\n        _req = _emptyFunc;\n      },\n      lagSmoothing: function lagSmoothing(threshold, adjustedLag) {\n        _lagThreshold = threshold || Infinity;\n        _adjustedLag = Math.min(adjustedLag || 33, _lagThreshold);\n      },\n      fps: function fps(_fps) {\n        _gap = 1000 / (_fps || 240);\n        _nextTime = _self.time * 1000 + _gap;\n      },\n      add: function add(callback, once, prioritize) {\n        var func = once ? function (t, d, f, v) {\n          callback(t, d, f, v);\n\n          _self.remove(func);\n        } : callback;\n\n        _self.remove(callback);\n\n        _listeners[prioritize ? \"unshift\" : \"push\"](func);\n\n        _wake();\n\n        return func;\n      },\n      remove: function remove(callback, i) {\n        ~(i = _listeners.indexOf(callback)) && _listeners.splice(i, 1) && _i >= i && _i--;\n      },\n      _listeners: _listeners\n    };\n    return _self;\n  }(),\n      _wake = function _wake() {\n    return !_tickerActive && _ticker.wake();\n  },\n      _easeMap = {},\n      _customEaseExp = /^[\\d.\\-M][\\d.\\-,\\s]/,\n      _quotesExp = /[\"']/g,\n      _parseObjectInString = function _parseObjectInString(value) {\n    var obj = {},\n        split = value.substr(1, value.length - 3).split(\":\"),\n        key = split[0],\n        i = 1,\n        l = split.length,\n        index,\n        val,\n        parsedVal;\n\n    for (; i < l; i++) {\n      val = split[i];\n      index = i !== l - 1 ? val.lastIndexOf(\",\") : val.length;\n      parsedVal = val.substr(0, index);\n      obj[key] = isNaN(parsedVal) ? parsedVal.replace(_quotesExp, \"\").trim() : +parsedVal;\n      key = val.substr(index + 1).trim();\n    }\n\n    return obj;\n  },\n      _valueInParentheses = function _valueInParentheses(value) {\n    var open = value.indexOf(\"(\") + 1,\n        close = value.indexOf(\")\"),\n        nested = value.indexOf(\"(\", open);\n    return value.substring(open, ~nested && nested < close ? value.indexOf(\")\", close + 1) : close);\n  },\n      _configEaseFromString = function _configEaseFromString(name) {\n    var split = (name + \"\").split(\"(\"),\n        ease = _easeMap[split[0]];\n    return ease && split.length > 1 && ease.config ? ease.config.apply(null, ~name.indexOf(\"{\") ? [_parseObjectInString(split[1])] : _valueInParentheses(name).split(\",\").map(_numericIfPossible)) : _easeMap._CE && _customEaseExp.test(name) ? _easeMap._CE(\"\", name) : ease;\n  },\n      _invertEase = function _invertEase(ease) {\n    return function (p) {\n      return 1 - ease(1 - p);\n    };\n  },\n      _propagateYoyoEase = function _propagateYoyoEase(timeline, isYoyo) {\n    var child = timeline._first,\n        ease;\n\n    while (child) {\n      if (child instanceof Timeline) {\n        _propagateYoyoEase(child, isYoyo);\n      } else if (child.vars.yoyoEase && (!child._yoyo || !child._repeat) && child._yoyo !== isYoyo) {\n        if (child.timeline) {\n          _propagateYoyoEase(child.timeline, isYoyo);\n        } else {\n          ease = child._ease;\n          child._ease = child._yEase;\n          child._yEase = ease;\n          child._yoyo = isYoyo;\n        }\n      }\n\n      child = child._next;\n    }\n  },\n      _parseEase = function _parseEase(ease, defaultEase) {\n    return !ease ? defaultEase : (_isFunction(ease) ? ease : _easeMap[ease] || _configEaseFromString(ease)) || defaultEase;\n  },\n      _insertEase = function _insertEase(names, easeIn, easeOut, easeInOut) {\n    if (easeOut === void 0) {\n      easeOut = function easeOut(p) {\n        return 1 - easeIn(1 - p);\n      };\n    }\n\n    if (easeInOut === void 0) {\n      easeInOut = function easeInOut(p) {\n        return p < .5 ? easeIn(p * 2) / 2 : 1 - easeIn((1 - p) * 2) / 2;\n      };\n    }\n\n    var ease = {\n      easeIn: easeIn,\n      easeOut: easeOut,\n      easeInOut: easeInOut\n    },\n        lowercaseName;\n\n    _forEachName(names, function (name) {\n      _easeMap[name] = _globals[name] = ease;\n      _easeMap[lowercaseName = name.toLowerCase()] = easeOut;\n\n      for (var p in ease) {\n        _easeMap[lowercaseName + (p === \"easeIn\" ? \".in\" : p === \"easeOut\" ? \".out\" : \".inOut\")] = _easeMap[name + \".\" + p] = ease[p];\n      }\n    });\n\n    return ease;\n  },\n      _easeInOutFromOut = function _easeInOutFromOut(easeOut) {\n    return function (p) {\n      return p < .5 ? (1 - easeOut(1 - p * 2)) / 2 : .5 + easeOut((p - .5) * 2) / 2;\n    };\n  },\n      _configElastic = function _configElastic(type, amplitude, period) {\n    var p1 = amplitude >= 1 ? amplitude : 1,\n        p2 = (period || (type ? .3 : .45)) / (amplitude < 1 ? amplitude : 1),\n        p3 = p2 / _2PI * (Math.asin(1 / p1) || 0),\n        easeOut = function easeOut(p) {\n      return p === 1 ? 1 : p1 * Math.pow(2, -10 * p) * _sin((p - p3) * p2) + 1;\n    },\n        ease = type === \"out\" ? easeOut : type === \"in\" ? function (p) {\n      return 1 - easeOut(1 - p);\n    } : _easeInOutFromOut(easeOut);\n\n    p2 = _2PI / p2;\n\n    ease.config = function (amplitude, period) {\n      return _configElastic(type, amplitude, period);\n    };\n\n    return ease;\n  },\n      _configBack = function _configBack(type, overshoot) {\n    if (overshoot === void 0) {\n      overshoot = 1.70158;\n    }\n\n    var easeOut = function easeOut(p) {\n      return p ? --p * p * ((overshoot + 1) * p + overshoot) + 1 : 0;\n    },\n        ease = type === \"out\" ? easeOut : type === \"in\" ? function (p) {\n      return 1 - easeOut(1 - p);\n    } : _easeInOutFromOut(easeOut);\n\n    ease.config = function (overshoot) {\n      return _configBack(type, overshoot);\n    };\n\n    return ease;\n  };\n\n  _forEachName(\"Linear,Quad,Cubic,Quart,Quint,Strong\", function (name, i) {\n    var power = i < 5 ? i + 1 : i;\n\n    _insertEase(name + \",Power\" + (power - 1), i ? function (p) {\n      return Math.pow(p, power);\n    } : function (p) {\n      return p;\n    }, function (p) {\n      return 1 - Math.pow(1 - p, power);\n    }, function (p) {\n      return p < .5 ? Math.pow(p * 2, power) / 2 : 1 - Math.pow((1 - p) * 2, power) / 2;\n    });\n  });\n\n  _easeMap.Linear.easeNone = _easeMap.none = _easeMap.Linear.easeIn;\n\n  _insertEase(\"Elastic\", _configElastic(\"in\"), _configElastic(\"out\"), _configElastic());\n\n  (function (n, c) {\n    var n1 = 1 / c,\n        n2 = 2 * n1,\n        n3 = 2.5 * n1,\n        easeOut = function easeOut(p) {\n      return p < n1 ? n * p * p : p < n2 ? n * Math.pow(p - 1.5 / c, 2) + .75 : p < n3 ? n * (p -= 2.25 / c) * p + .9375 : n * Math.pow(p - 2.625 / c, 2) + .984375;\n    };\n\n    _insertEase(\"Bounce\", function (p) {\n      return 1 - easeOut(1 - p);\n    }, easeOut);\n  })(7.5625, 2.75);\n\n  _insertEase(\"Expo\", function (p) {\n    return Math.pow(2, 10 * (p - 1)) * p + p * p * p * p * p * p * (1 - p);\n  });\n\n  _insertEase(\"Circ\", function (p) {\n    return -(_sqrt(1 - p * p) - 1);\n  });\n\n  _insertEase(\"Sine\", function (p) {\n    return p === 1 ? 1 : -_cos(p * _HALF_PI) + 1;\n  });\n\n  _insertEase(\"Back\", _configBack(\"in\"), _configBack(\"out\"), _configBack());\n\n  _easeMap.SteppedEase = _easeMap.steps = _globals.SteppedEase = {\n    config: function config(steps, immediateStart) {\n      if (steps === void 0) {\n        steps = 1;\n      }\n\n      var p1 = 1 / steps,\n          p2 = steps + (immediateStart ? 0 : 1),\n          p3 = immediateStart ? 1 : 0,\n          max = 1 - _tinyNum;\n      return function (p) {\n        return ((p2 * _clamp(0, max, p) | 0) + p3) * p1;\n      };\n    }\n  };\n  _defaults.ease = _easeMap[\"quad.out\"];\n\n  _forEachName(\"onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt\", function (name) {\n    return _callbackNames += name + \",\" + name + \"Params,\";\n  });\n\n  var GSCache = function GSCache(target, harness) {\n    this.id = _gsID++;\n    target._gsap = this;\n    this.target = target;\n    this.harness = harness;\n    this.get = harness ? harness.get : _getProperty;\n    this.set = harness ? harness.getSetter : _getSetter;\n  };\n  var Animation = function () {\n    function Animation(vars) {\n      this.vars = vars;\n      this._delay = +vars.delay || 0;\n\n      if (this._repeat = vars.repeat === Infinity ? -2 : vars.repeat || 0) {\n        this._rDelay = vars.repeatDelay || 0;\n        this._yoyo = !!vars.yoyo || !!vars.yoyoEase;\n      }\n\n      this._ts = 1;\n\n      _setDuration(this, +vars.duration, 1, 1);\n\n      this.data = vars.data;\n\n      if (_context) {\n        this._ctx = _context;\n\n        _context.data.push(this);\n      }\n\n      _tickerActive || _ticker.wake();\n    }\n\n    var _proto = Animation.prototype;\n\n    _proto.delay = function delay(value) {\n      if (value || value === 0) {\n        this.parent && this.parent.smoothChildTiming && this.startTime(this._start + value - this._delay);\n        this._delay = value;\n        return this;\n      }\n\n      return this._delay;\n    };\n\n    _proto.duration = function duration(value) {\n      return arguments.length ? this.totalDuration(this._repeat > 0 ? value + (value + this._rDelay) * this._repeat : value) : this.totalDuration() && this._dur;\n    };\n\n    _proto.totalDuration = function totalDuration(value) {\n      if (!arguments.length) {\n        return this._tDur;\n      }\n\n      this._dirty = 0;\n      return _setDuration(this, this._repeat < 0 ? value : (value - this._repeat * this._rDelay) / (this._repeat + 1));\n    };\n\n    _proto.totalTime = function totalTime(_totalTime, suppressEvents) {\n      _wake();\n\n      if (!arguments.length) {\n        return this._tTime;\n      }\n\n      var parent = this._dp;\n\n      if (parent && parent.smoothChildTiming && this._ts) {\n        _alignPlayhead(this, _totalTime);\n\n        !parent._dp || parent.parent || _postAddChecks(parent, this);\n\n        while (parent && parent.parent) {\n          if (parent.parent._time !== parent._start + (parent._ts >= 0 ? parent._tTime / parent._ts : (parent.totalDuration() - parent._tTime) / -parent._ts)) {\n            parent.totalTime(parent._tTime, true);\n          }\n\n          parent = parent.parent;\n        }\n\n        if (!this.parent && this._dp.autoRemoveChildren && (this._ts > 0 && _totalTime < this._tDur || this._ts < 0 && _totalTime > 0 || !this._tDur && !_totalTime)) {\n          _addToTimeline(this._dp, this, this._start - this._delay);\n        }\n      }\n\n      if (this._tTime !== _totalTime || !this._dur && !suppressEvents || this._initted && Math.abs(this._zTime) === _tinyNum || !_totalTime && !this._initted && (this.add || this._ptLookup)) {\n        this._ts || (this._pTime = _totalTime);\n\n        _lazySafeRender(this, _totalTime, suppressEvents);\n      }\n\n      return this;\n    };\n\n    _proto.time = function time(value, suppressEvents) {\n      return arguments.length ? this.totalTime(Math.min(this.totalDuration(), value + _elapsedCycleDuration(this)) % (this._dur + this._rDelay) || (value ? this._dur : 0), suppressEvents) : this._time;\n    };\n\n    _proto.totalProgress = function totalProgress(value, suppressEvents) {\n      return arguments.length ? this.totalTime(this.totalDuration() * value, suppressEvents) : this.totalDuration() ? Math.min(1, this._tTime / this._tDur) : this.rawTime() >= 0 && this._initted ? 1 : 0;\n    };\n\n    _proto.progress = function progress(value, suppressEvents) {\n      return arguments.length ? this.totalTime(this.duration() * (this._yoyo && !(this.iteration() & 1) ? 1 - value : value) + _elapsedCycleDuration(this), suppressEvents) : this.duration() ? Math.min(1, this._time / this._dur) : this.rawTime() > 0 ? 1 : 0;\n    };\n\n    _proto.iteration = function iteration(value, suppressEvents) {\n      var cycleDuration = this.duration() + this._rDelay;\n\n      return arguments.length ? this.totalTime(this._time + (value - 1) * cycleDuration, suppressEvents) : this._repeat ? _animationCycle(this._tTime, cycleDuration) + 1 : 1;\n    };\n\n    _proto.timeScale = function timeScale(value, suppressEvents) {\n      if (!arguments.length) {\n        return this._rts === -_tinyNum ? 0 : this._rts;\n      }\n\n      if (this._rts === value) {\n        return this;\n      }\n\n      var tTime = this.parent && this._ts ? _parentToChildTotalTime(this.parent._time, this) : this._tTime;\n      this._rts = +value || 0;\n      this._ts = this._ps || value === -_tinyNum ? 0 : this._rts;\n      this.totalTime(_clamp(-Math.abs(this._delay), this.totalDuration(), tTime), suppressEvents !== false);\n\n      _setEnd(this);\n\n      return _recacheAncestors(this);\n    };\n\n    _proto.paused = function paused(value) {\n      if (!arguments.length) {\n        return this._ps;\n      }\n\n      if (this._ps !== value) {\n        this._ps = value;\n\n        if (value) {\n          this._pTime = this._tTime || Math.max(-this._delay, this.rawTime());\n          this._ts = this._act = 0;\n        } else {\n          _wake();\n\n          this._ts = this._rts;\n          this.totalTime(this.parent && !this.parent.smoothChildTiming ? this.rawTime() : this._tTime || this._pTime, this.progress() === 1 && Math.abs(this._zTime) !== _tinyNum && (this._tTime -= _tinyNum));\n        }\n      }\n\n      return this;\n    };\n\n    _proto.startTime = function startTime(value) {\n      if (arguments.length) {\n        this._start = value;\n        var parent = this.parent || this._dp;\n        parent && (parent._sort || !this.parent) && _addToTimeline(parent, this, value - this._delay);\n        return this;\n      }\n\n      return this._start;\n    };\n\n    _proto.endTime = function endTime(includeRepeats) {\n      return this._start + (_isNotFalse(includeRepeats) ? this.totalDuration() : this.duration()) / Math.abs(this._ts || 1);\n    };\n\n    _proto.rawTime = function rawTime(wrapRepeats) {\n      var parent = this.parent || this._dp;\n      return !parent ? this._tTime : wrapRepeats && (!this._ts || this._repeat && this._time && this.totalProgress() < 1) ? this._tTime % (this._dur + this._rDelay) : !this._ts ? this._tTime : _parentToChildTotalTime(parent.rawTime(wrapRepeats), this);\n    };\n\n    _proto.revert = function revert(config) {\n      if (config === void 0) {\n        config = _revertConfig;\n      }\n\n      var prevIsReverting = _reverting;\n      _reverting = config;\n\n      if (_isRevertWorthy(this)) {\n        this.timeline && this.timeline.revert(config);\n        this.totalTime(-0.01, config.suppressEvents);\n      }\n\n      this.data !== \"nested\" && config.kill !== false && this.kill();\n      _reverting = prevIsReverting;\n      return this;\n    };\n\n    _proto.globalTime = function globalTime(rawTime) {\n      var animation = this,\n          time = arguments.length ? rawTime : animation.rawTime();\n\n      while (animation) {\n        time = animation._start + time / (Math.abs(animation._ts) || 1);\n        animation = animation._dp;\n      }\n\n      return !this.parent && this._sat ? this._sat.globalTime(rawTime) : time;\n    };\n\n    _proto.repeat = function repeat(value) {\n      if (arguments.length) {\n        this._repeat = value === Infinity ? -2 : value;\n        return _onUpdateTotalDuration(this);\n      }\n\n      return this._repeat === -2 ? Infinity : this._repeat;\n    };\n\n    _proto.repeatDelay = function repeatDelay(value) {\n      if (arguments.length) {\n        var time = this._time;\n        this._rDelay = value;\n\n        _onUpdateTotalDuration(this);\n\n        return time ? this.time(time) : this;\n      }\n\n      return this._rDelay;\n    };\n\n    _proto.yoyo = function yoyo(value) {\n      if (arguments.length) {\n        this._yoyo = value;\n        return this;\n      }\n\n      return this._yoyo;\n    };\n\n    _proto.seek = function seek(position, suppressEvents) {\n      return this.totalTime(_parsePosition(this, position), _isNotFalse(suppressEvents));\n    };\n\n    _proto.restart = function restart(includeDelay, suppressEvents) {\n      this.play().totalTime(includeDelay ? -this._delay : 0, _isNotFalse(suppressEvents));\n      this._dur || (this._zTime = -_tinyNum);\n      return this;\n    };\n\n    _proto.play = function play(from, suppressEvents) {\n      from != null && this.seek(from, suppressEvents);\n      return this.reversed(false).paused(false);\n    };\n\n    _proto.reverse = function reverse(from, suppressEvents) {\n      from != null && this.seek(from || this.totalDuration(), suppressEvents);\n      return this.reversed(true).paused(false);\n    };\n\n    _proto.pause = function pause(atTime, suppressEvents) {\n      atTime != null && this.seek(atTime, suppressEvents);\n      return this.paused(true);\n    };\n\n    _proto.resume = function resume() {\n      return this.paused(false);\n    };\n\n    _proto.reversed = function reversed(value) {\n      if (arguments.length) {\n        !!value !== this.reversed() && this.timeScale(-this._rts || (value ? -_tinyNum : 0));\n        return this;\n      }\n\n      return this._rts < 0;\n    };\n\n    _proto.invalidate = function invalidate() {\n      this._initted = this._act = 0;\n      this._zTime = -_tinyNum;\n      return this;\n    };\n\n    _proto.isActive = function isActive() {\n      var parent = this.parent || this._dp,\n          start = this._start,\n          rawTime;\n      return !!(!parent || this._ts && this._initted && parent.isActive() && (rawTime = parent.rawTime(true)) >= start && rawTime < this.endTime(true) - _tinyNum);\n    };\n\n    _proto.eventCallback = function eventCallback(type, callback, params) {\n      var vars = this.vars;\n\n      if (arguments.length > 1) {\n        if (!callback) {\n          delete vars[type];\n        } else {\n          vars[type] = callback;\n          params && (vars[type + \"Params\"] = params);\n          type === \"onUpdate\" && (this._onUpdate = callback);\n        }\n\n        return this;\n      }\n\n      return vars[type];\n    };\n\n    _proto.then = function then(onFulfilled) {\n      var self = this;\n      return new Promise(function (resolve) {\n        var f = _isFunction(onFulfilled) ? onFulfilled : _passThrough,\n            _resolve = function _resolve() {\n          var _then = self.then;\n          self.then = null;\n          _isFunction(f) && (f = f(self)) && (f.then || f === self) && (self.then = _then);\n          resolve(f);\n          self.then = _then;\n        };\n\n        if (self._initted && self.totalProgress() === 1 && self._ts >= 0 || !self._tTime && self._ts < 0) {\n          _resolve();\n        } else {\n          self._prom = _resolve;\n        }\n      });\n    };\n\n    _proto.kill = function kill() {\n      _interrupt(this);\n    };\n\n    return Animation;\n  }();\n\n  _setDefaults(Animation.prototype, {\n    _time: 0,\n    _start: 0,\n    _end: 0,\n    _tTime: 0,\n    _tDur: 0,\n    _dirty: 0,\n    _repeat: 0,\n    _yoyo: false,\n    parent: null,\n    _initted: false,\n    _rDelay: 0,\n    _ts: 1,\n    _dp: 0,\n    ratio: 0,\n    _zTime: -_tinyNum,\n    _prom: 0,\n    _ps: false,\n    _rts: 1\n  });\n\n  var Timeline = function (_Animation) {\n    _inheritsLoose(Timeline, _Animation);\n\n    function Timeline(vars, position) {\n      var _this;\n\n      if (vars === void 0) {\n        vars = {};\n      }\n\n      _this = _Animation.call(this, vars) || this;\n      _this.labels = {};\n      _this.smoothChildTiming = !!vars.smoothChildTiming;\n      _this.autoRemoveChildren = !!vars.autoRemoveChildren;\n      _this._sort = _isNotFalse(vars.sortChildren);\n      _globalTimeline && _addToTimeline(vars.parent || _globalTimeline, _assertThisInitialized(_this), position);\n      vars.reversed && _this.reverse();\n      vars.paused && _this.paused(true);\n      vars.scrollTrigger && _scrollTrigger(_assertThisInitialized(_this), vars.scrollTrigger);\n      return _this;\n    }\n\n    var _proto2 = Timeline.prototype;\n\n    _proto2.to = function to(targets, vars, position) {\n      _createTweenType(0, arguments, this);\n\n      return this;\n    };\n\n    _proto2.from = function from(targets, vars, position) {\n      _createTweenType(1, arguments, this);\n\n      return this;\n    };\n\n    _proto2.fromTo = function fromTo(targets, fromVars, toVars, position) {\n      _createTweenType(2, arguments, this);\n\n      return this;\n    };\n\n    _proto2.set = function set(targets, vars, position) {\n      vars.duration = 0;\n      vars.parent = this;\n      _inheritDefaults(vars).repeatDelay || (vars.repeat = 0);\n      vars.immediateRender = !!vars.immediateRender;\n      new Tween(targets, vars, _parsePosition(this, position), 1);\n      return this;\n    };\n\n    _proto2.call = function call(callback, params, position) {\n      return _addToTimeline(this, Tween.delayedCall(0, callback, params), position);\n    };\n\n    _proto2.staggerTo = function staggerTo(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams) {\n      vars.duration = duration;\n      vars.stagger = vars.stagger || stagger;\n      vars.onComplete = onCompleteAll;\n      vars.onCompleteParams = onCompleteAllParams;\n      vars.parent = this;\n      new Tween(targets, vars, _parsePosition(this, position));\n      return this;\n    };\n\n    _proto2.staggerFrom = function staggerFrom(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams) {\n      vars.runBackwards = 1;\n      _inheritDefaults(vars).immediateRender = _isNotFalse(vars.immediateRender);\n      return this.staggerTo(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams);\n    };\n\n    _proto2.staggerFromTo = function staggerFromTo(targets, duration, fromVars, toVars, stagger, position, onCompleteAll, onCompleteAllParams) {\n      toVars.startAt = fromVars;\n      _inheritDefaults(toVars).immediateRender = _isNotFalse(toVars.immediateRender);\n      return this.staggerTo(targets, duration, toVars, stagger, position, onCompleteAll, onCompleteAllParams);\n    };\n\n    _proto2.render = function render(totalTime, suppressEvents, force) {\n      var prevTime = this._time,\n          tDur = this._dirty ? this.totalDuration() : this._tDur,\n          dur = this._dur,\n          tTime = totalTime <= 0 ? 0 : _roundPrecise(totalTime),\n          crossingStart = this._zTime < 0 !== totalTime < 0 && (this._initted || !dur),\n          time,\n          child,\n          next,\n          iteration,\n          cycleDuration,\n          prevPaused,\n          pauseTween,\n          timeScale,\n          prevStart,\n          prevIteration,\n          yoyo,\n          isYoyo;\n      this !== _globalTimeline && tTime > tDur && totalTime >= 0 && (tTime = tDur);\n\n      if (tTime !== this._tTime || force || crossingStart) {\n        if (prevTime !== this._time && dur) {\n          tTime += this._time - prevTime;\n          totalTime += this._time - prevTime;\n        }\n\n        time = tTime;\n        prevStart = this._start;\n        timeScale = this._ts;\n        prevPaused = !timeScale;\n\n        if (crossingStart) {\n          dur || (prevTime = this._zTime);\n          (totalTime || !suppressEvents) && (this._zTime = totalTime);\n        }\n\n        if (this._repeat) {\n          yoyo = this._yoyo;\n          cycleDuration = dur + this._rDelay;\n\n          if (this._repeat < -1 && totalTime < 0) {\n            return this.totalTime(cycleDuration * 100 + totalTime, suppressEvents, force);\n          }\n\n          time = _roundPrecise(tTime % cycleDuration);\n\n          if (tTime === tDur) {\n            iteration = this._repeat;\n            time = dur;\n          } else {\n            prevIteration = _roundPrecise(tTime / cycleDuration);\n            iteration = ~~prevIteration;\n\n            if (iteration && iteration === prevIteration) {\n              time = dur;\n              iteration--;\n            }\n\n            time > dur && (time = dur);\n          }\n\n          prevIteration = _animationCycle(this._tTime, cycleDuration);\n          !prevTime && this._tTime && prevIteration !== iteration && this._tTime - prevIteration * cycleDuration - this._dur <= 0 && (prevIteration = iteration);\n\n          if (yoyo && iteration & 1) {\n            time = dur - time;\n            isYoyo = 1;\n          }\n\n          if (iteration !== prevIteration && !this._lock) {\n            var rewinding = yoyo && prevIteration & 1,\n                doesWrap = rewinding === (yoyo && iteration & 1);\n            iteration < prevIteration && (rewinding = !rewinding);\n            prevTime = rewinding ? 0 : tTime % dur ? dur : tTime;\n            this._lock = 1;\n            this.render(prevTime || (isYoyo ? 0 : _roundPrecise(iteration * cycleDuration)), suppressEvents, !dur)._lock = 0;\n            this._tTime = tTime;\n            !suppressEvents && this.parent && _callback(this, \"onRepeat\");\n            this.vars.repeatRefresh && !isYoyo && (this.invalidate()._lock = 1);\n\n            if (prevTime && prevTime !== this._time || prevPaused !== !this._ts || this.vars.onRepeat && !this.parent && !this._act) {\n              return this;\n            }\n\n            dur = this._dur;\n            tDur = this._tDur;\n\n            if (doesWrap) {\n              this._lock = 2;\n              prevTime = rewinding ? dur : -0.0001;\n              this.render(prevTime, true);\n              this.vars.repeatRefresh && !isYoyo && this.invalidate();\n            }\n\n            this._lock = 0;\n\n            if (!this._ts && !prevPaused) {\n              return this;\n            }\n\n            _propagateYoyoEase(this, isYoyo);\n          }\n        }\n\n        if (this._hasPause && !this._forcing && this._lock < 2) {\n          pauseTween = _findNextPauseTween(this, _roundPrecise(prevTime), _roundPrecise(time));\n\n          if (pauseTween) {\n            tTime -= time - (time = pauseTween._start);\n          }\n        }\n\n        this._tTime = tTime;\n        this._time = time;\n        this._act = !timeScale;\n\n        if (!this._initted) {\n          this._onUpdate = this.vars.onUpdate;\n          this._initted = 1;\n          this._zTime = totalTime;\n          prevTime = 0;\n        }\n\n        if (!prevTime && tTime && !suppressEvents && !prevIteration) {\n          _callback(this, \"onStart\");\n\n          if (this._tTime !== tTime) {\n            return this;\n          }\n        }\n\n        if (time >= prevTime && totalTime >= 0) {\n          child = this._first;\n\n          while (child) {\n            next = child._next;\n\n            if ((child._act || time >= child._start) && child._ts && pauseTween !== child) {\n              if (child.parent !== this) {\n                return this.render(totalTime, suppressEvents, force);\n              }\n\n              child.render(child._ts > 0 ? (time - child._start) * child._ts : (child._dirty ? child.totalDuration() : child._tDur) + (time - child._start) * child._ts, suppressEvents, force);\n\n              if (time !== this._time || !this._ts && !prevPaused) {\n                pauseTween = 0;\n                next && (tTime += this._zTime = -_tinyNum);\n                break;\n              }\n            }\n\n            child = next;\n          }\n        } else {\n          child = this._last;\n          var adjustedTime = totalTime < 0 ? totalTime : time;\n\n          while (child) {\n            next = child._prev;\n\n            if ((child._act || adjustedTime <= child._end) && child._ts && pauseTween !== child) {\n              if (child.parent !== this) {\n                return this.render(totalTime, suppressEvents, force);\n              }\n\n              child.render(child._ts > 0 ? (adjustedTime - child._start) * child._ts : (child._dirty ? child.totalDuration() : child._tDur) + (adjustedTime - child._start) * child._ts, suppressEvents, force || _reverting && _isRevertWorthy(child));\n\n              if (time !== this._time || !this._ts && !prevPaused) {\n                pauseTween = 0;\n                next && (tTime += this._zTime = adjustedTime ? -_tinyNum : _tinyNum);\n                break;\n              }\n            }\n\n            child = next;\n          }\n        }\n\n        if (pauseTween && !suppressEvents) {\n          this.pause();\n          pauseTween.render(time >= prevTime ? 0 : -_tinyNum)._zTime = time >= prevTime ? 1 : -1;\n\n          if (this._ts) {\n            this._start = prevStart;\n\n            _setEnd(this);\n\n            return this.render(totalTime, suppressEvents, force);\n          }\n        }\n\n        this._onUpdate && !suppressEvents && _callback(this, \"onUpdate\", true);\n        if (tTime === tDur && this._tTime >= this.totalDuration() || !tTime && prevTime) if (prevStart === this._start || Math.abs(timeScale) !== Math.abs(this._ts)) if (!this._lock) {\n          (totalTime || !dur) && (tTime === tDur && this._ts > 0 || !tTime && this._ts < 0) && _removeFromParent(this, 1);\n\n          if (!suppressEvents && !(totalTime < 0 && !prevTime) && (tTime || prevTime || !tDur)) {\n            _callback(this, tTime === tDur && totalTime >= 0 ? \"onComplete\" : \"onReverseComplete\", true);\n\n            this._prom && !(tTime < tDur && this.timeScale() > 0) && this._prom();\n          }\n        }\n      }\n\n      return this;\n    };\n\n    _proto2.add = function add(child, position) {\n      var _this2 = this;\n\n      _isNumber(position) || (position = _parsePosition(this, position, child));\n\n      if (!(child instanceof Animation)) {\n        if (_isArray(child)) {\n          child.forEach(function (obj) {\n            return _this2.add(obj, position);\n          });\n          return this;\n        }\n\n        if (_isString(child)) {\n          return this.addLabel(child, position);\n        }\n\n        if (_isFunction(child)) {\n          child = Tween.delayedCall(0, child);\n        } else {\n          return this;\n        }\n      }\n\n      return this !== child ? _addToTimeline(this, child, position) : this;\n    };\n\n    _proto2.getChildren = function getChildren(nested, tweens, timelines, ignoreBeforeTime) {\n      if (nested === void 0) {\n        nested = true;\n      }\n\n      if (tweens === void 0) {\n        tweens = true;\n      }\n\n      if (timelines === void 0) {\n        timelines = true;\n      }\n\n      if (ignoreBeforeTime === void 0) {\n        ignoreBeforeTime = -_bigNum;\n      }\n\n      var a = [],\n          child = this._first;\n\n      while (child) {\n        if (child._start >= ignoreBeforeTime) {\n          if (child instanceof Tween) {\n            tweens && a.push(child);\n          } else {\n            timelines && a.push(child);\n            nested && a.push.apply(a, child.getChildren(true, tweens, timelines));\n          }\n        }\n\n        child = child._next;\n      }\n\n      return a;\n    };\n\n    _proto2.getById = function getById(id) {\n      var animations = this.getChildren(1, 1, 1),\n          i = animations.length;\n\n      while (i--) {\n        if (animations[i].vars.id === id) {\n          return animations[i];\n        }\n      }\n    };\n\n    _proto2.remove = function remove(child) {\n      if (_isString(child)) {\n        return this.removeLabel(child);\n      }\n\n      if (_isFunction(child)) {\n        return this.killTweensOf(child);\n      }\n\n      child.parent === this && _removeLinkedListItem(this, child);\n\n      if (child === this._recent) {\n        this._recent = this._last;\n      }\n\n      return _uncache(this);\n    };\n\n    _proto2.totalTime = function totalTime(_totalTime2, suppressEvents) {\n      if (!arguments.length) {\n        return this._tTime;\n      }\n\n      this._forcing = 1;\n\n      if (!this._dp && this._ts) {\n        this._start = _roundPrecise(_ticker.time - (this._ts > 0 ? _totalTime2 / this._ts : (this.totalDuration() - _totalTime2) / -this._ts));\n      }\n\n      _Animation.prototype.totalTime.call(this, _totalTime2, suppressEvents);\n\n      this._forcing = 0;\n      return this;\n    };\n\n    _proto2.addLabel = function addLabel(label, position) {\n      this.labels[label] = _parsePosition(this, position);\n      return this;\n    };\n\n    _proto2.removeLabel = function removeLabel(label) {\n      delete this.labels[label];\n      return this;\n    };\n\n    _proto2.addPause = function addPause(position, callback, params) {\n      var t = Tween.delayedCall(0, callback || _emptyFunc, params);\n      t.data = \"isPause\";\n      this._hasPause = 1;\n      return _addToTimeline(this, t, _parsePosition(this, position));\n    };\n\n    _proto2.removePause = function removePause(position) {\n      var child = this._first;\n      position = _parsePosition(this, position);\n\n      while (child) {\n        if (child._start === position && child.data === \"isPause\") {\n          _removeFromParent(child);\n        }\n\n        child = child._next;\n      }\n    };\n\n    _proto2.killTweensOf = function killTweensOf(targets, props, onlyActive) {\n      var tweens = this.getTweensOf(targets, onlyActive),\n          i = tweens.length;\n\n      while (i--) {\n        _overwritingTween !== tweens[i] && tweens[i].kill(targets, props);\n      }\n\n      return this;\n    };\n\n    _proto2.getTweensOf = function getTweensOf(targets, onlyActive) {\n      var a = [],\n          parsedTargets = toArray(targets),\n          child = this._first,\n          isGlobalTime = _isNumber(onlyActive),\n          children;\n\n      while (child) {\n        if (child instanceof Tween) {\n          if (_arrayContainsAny(child._targets, parsedTargets) && (isGlobalTime ? (!_overwritingTween || child._initted && child._ts) && child.globalTime(0) <= onlyActive && child.globalTime(child.totalDuration()) > onlyActive : !onlyActive || child.isActive())) {\n            a.push(child);\n          }\n        } else if ((children = child.getTweensOf(parsedTargets, onlyActive)).length) {\n          a.push.apply(a, children);\n        }\n\n        child = child._next;\n      }\n\n      return a;\n    };\n\n    _proto2.tweenTo = function tweenTo(position, vars) {\n      vars = vars || {};\n\n      var tl = this,\n          endTime = _parsePosition(tl, position),\n          _vars = vars,\n          startAt = _vars.startAt,\n          _onStart = _vars.onStart,\n          onStartParams = _vars.onStartParams,\n          immediateRender = _vars.immediateRender,\n          initted,\n          tween = Tween.to(tl, _setDefaults({\n        ease: vars.ease || \"none\",\n        lazy: false,\n        immediateRender: false,\n        time: endTime,\n        overwrite: \"auto\",\n        duration: vars.duration || Math.abs((endTime - (startAt && \"time\" in startAt ? startAt.time : tl._time)) / tl.timeScale()) || _tinyNum,\n        onStart: function onStart() {\n          tl.pause();\n\n          if (!initted) {\n            var duration = vars.duration || Math.abs((endTime - (startAt && \"time\" in startAt ? startAt.time : tl._time)) / tl.timeScale());\n            tween._dur !== duration && _setDuration(tween, duration, 0, 1).render(tween._time, true, true);\n            initted = 1;\n          }\n\n          _onStart && _onStart.apply(tween, onStartParams || []);\n        }\n      }, vars));\n\n      return immediateRender ? tween.render(0) : tween;\n    };\n\n    _proto2.tweenFromTo = function tweenFromTo(fromPosition, toPosition, vars) {\n      return this.tweenTo(toPosition, _setDefaults({\n        startAt: {\n          time: _parsePosition(this, fromPosition)\n        }\n      }, vars));\n    };\n\n    _proto2.recent = function recent() {\n      return this._recent;\n    };\n\n    _proto2.nextLabel = function nextLabel(afterTime) {\n      if (afterTime === void 0) {\n        afterTime = this._time;\n      }\n\n      return _getLabelInDirection(this, _parsePosition(this, afterTime));\n    };\n\n    _proto2.previousLabel = function previousLabel(beforeTime) {\n      if (beforeTime === void 0) {\n        beforeTime = this._time;\n      }\n\n      return _getLabelInDirection(this, _parsePosition(this, beforeTime), 1);\n    };\n\n    _proto2.currentLabel = function currentLabel(value) {\n      return arguments.length ? this.seek(value, true) : this.previousLabel(this._time + _tinyNum);\n    };\n\n    _proto2.shiftChildren = function shiftChildren(amount, adjustLabels, ignoreBeforeTime) {\n      if (ignoreBeforeTime === void 0) {\n        ignoreBeforeTime = 0;\n      }\n\n      var child = this._first,\n          labels = this.labels,\n          p;\n\n      while (child) {\n        if (child._start >= ignoreBeforeTime) {\n          child._start += amount;\n          child._end += amount;\n        }\n\n        child = child._next;\n      }\n\n      if (adjustLabels) {\n        for (p in labels) {\n          if (labels[p] >= ignoreBeforeTime) {\n            labels[p] += amount;\n          }\n        }\n      }\n\n      return _uncache(this);\n    };\n\n    _proto2.invalidate = function invalidate(soft) {\n      var child = this._first;\n      this._lock = 0;\n\n      while (child) {\n        child.invalidate(soft);\n        child = child._next;\n      }\n\n      return _Animation.prototype.invalidate.call(this, soft);\n    };\n\n    _proto2.clear = function clear(includeLabels) {\n      if (includeLabels === void 0) {\n        includeLabels = true;\n      }\n\n      var child = this._first,\n          next;\n\n      while (child) {\n        next = child._next;\n        this.remove(child);\n        child = next;\n      }\n\n      this._dp && (this._time = this._tTime = this._pTime = 0);\n      includeLabels && (this.labels = {});\n      return _uncache(this);\n    };\n\n    _proto2.totalDuration = function totalDuration(value) {\n      var max = 0,\n          self = this,\n          child = self._last,\n          prevStart = _bigNum,\n          prev,\n          start,\n          parent;\n\n      if (arguments.length) {\n        return self.timeScale((self._repeat < 0 ? self.duration() : self.totalDuration()) / (self.reversed() ? -value : value));\n      }\n\n      if (self._dirty) {\n        parent = self.parent;\n\n        while (child) {\n          prev = child._prev;\n          child._dirty && child.totalDuration();\n          start = child._start;\n\n          if (start > prevStart && self._sort && child._ts && !self._lock) {\n            self._lock = 1;\n            _addToTimeline(self, child, start - child._delay, 1)._lock = 0;\n          } else {\n            prevStart = start;\n          }\n\n          if (start < 0 && child._ts) {\n            max -= start;\n\n            if (!parent && !self._dp || parent && parent.smoothChildTiming) {\n              self._start += start / self._ts;\n              self._time -= start;\n              self._tTime -= start;\n            }\n\n            self.shiftChildren(-start, false, -1e999);\n            prevStart = 0;\n          }\n\n          child._end > max && child._ts && (max = child._end);\n          child = prev;\n        }\n\n        _setDuration(self, self === _globalTimeline && self._time > max ? self._time : max, 1, 1);\n\n        self._dirty = 0;\n      }\n\n      return self._tDur;\n    };\n\n    Timeline.updateRoot = function updateRoot(time) {\n      if (_globalTimeline._ts) {\n        _lazySafeRender(_globalTimeline, _parentToChildTotalTime(time, _globalTimeline));\n\n        _lastRenderedFrame = _ticker.frame;\n      }\n\n      if (_ticker.frame >= _nextGCFrame) {\n        _nextGCFrame += _config.autoSleep || 120;\n        var child = _globalTimeline._first;\n        if (!child || !child._ts) if (_config.autoSleep && _ticker._listeners.length < 2) {\n          while (child && !child._ts) {\n            child = child._next;\n          }\n\n          child || _ticker.sleep();\n        }\n      }\n    };\n\n    return Timeline;\n  }(Animation);\n\n  _setDefaults(Timeline.prototype, {\n    _lock: 0,\n    _hasPause: 0,\n    _forcing: 0\n  });\n\n  var _addComplexStringPropTween = function _addComplexStringPropTween(target, prop, start, end, setter, stringFilter, funcParam) {\n    var pt = new PropTween(this._pt, target, prop, 0, 1, _renderComplexString, null, setter),\n        index = 0,\n        matchIndex = 0,\n        result,\n        startNums,\n        color,\n        endNum,\n        chunk,\n        startNum,\n        hasRandom,\n        a;\n    pt.b = start;\n    pt.e = end;\n    start += \"\";\n    end += \"\";\n\n    if (hasRandom = ~end.indexOf(\"random(\")) {\n      end = _replaceRandom(end);\n    }\n\n    if (stringFilter) {\n      a = [start, end];\n      stringFilter(a, target, prop);\n      start = a[0];\n      end = a[1];\n    }\n\n    startNums = start.match(_complexStringNumExp) || [];\n\n    while (result = _complexStringNumExp.exec(end)) {\n      endNum = result[0];\n      chunk = end.substring(index, result.index);\n\n      if (color) {\n        color = (color + 1) % 5;\n      } else if (chunk.substr(-5) === \"rgba(\") {\n        color = 1;\n      }\n\n      if (endNum !== startNums[matchIndex++]) {\n        startNum = parseFloat(startNums[matchIndex - 1]) || 0;\n        pt._pt = {\n          _next: pt._pt,\n          p: chunk || matchIndex === 1 ? chunk : \",\",\n          s: startNum,\n          c: endNum.charAt(1) === \"=\" ? _parseRelative(startNum, endNum) - startNum : parseFloat(endNum) - startNum,\n          m: color && color < 4 ? Math.round : 0\n        };\n        index = _complexStringNumExp.lastIndex;\n      }\n    }\n\n    pt.c = index < end.length ? end.substring(index, end.length) : \"\";\n    pt.fp = funcParam;\n\n    if (_relExp.test(end) || hasRandom) {\n      pt.e = 0;\n    }\n\n    this._pt = pt;\n    return pt;\n  },\n      _addPropTween = function _addPropTween(target, prop, start, end, index, targets, modifier, stringFilter, funcParam, optional) {\n    _isFunction(end) && (end = end(index || 0, target, targets));\n    var currentValue = target[prop],\n        parsedStart = start !== \"get\" ? start : !_isFunction(currentValue) ? currentValue : funcParam ? target[prop.indexOf(\"set\") || !_isFunction(target[\"get\" + prop.substr(3)]) ? prop : \"get\" + prop.substr(3)](funcParam) : target[prop](),\n        setter = !_isFunction(currentValue) ? _setterPlain : funcParam ? _setterFuncWithParam : _setterFunc,\n        pt;\n\n    if (_isString(end)) {\n      if (~end.indexOf(\"random(\")) {\n        end = _replaceRandom(end);\n      }\n\n      if (end.charAt(1) === \"=\") {\n        pt = _parseRelative(parsedStart, end) + (getUnit(parsedStart) || 0);\n\n        if (pt || pt === 0) {\n          end = pt;\n        }\n      }\n    }\n\n    if (!optional || parsedStart !== end || _forceAllPropTweens) {\n      if (!isNaN(parsedStart * end) && end !== \"\") {\n        pt = new PropTween(this._pt, target, prop, +parsedStart || 0, end - (parsedStart || 0), typeof currentValue === \"boolean\" ? _renderBoolean : _renderPlain, 0, setter);\n        funcParam && (pt.fp = funcParam);\n        modifier && pt.modifier(modifier, this, target);\n        return this._pt = pt;\n      }\n\n      !currentValue && !(prop in target) && _missingPlugin(prop, end);\n      return _addComplexStringPropTween.call(this, target, prop, parsedStart, end, setter, stringFilter || _config.stringFilter, funcParam);\n    }\n  },\n      _processVars = function _processVars(vars, index, target, targets, tween) {\n    _isFunction(vars) && (vars = _parseFuncOrString(vars, tween, index, target, targets));\n\n    if (!_isObject(vars) || vars.style && vars.nodeType || _isArray(vars) || _isTypedArray(vars)) {\n      return _isString(vars) ? _parseFuncOrString(vars, tween, index, target, targets) : vars;\n    }\n\n    var copy = {},\n        p;\n\n    for (p in vars) {\n      copy[p] = _parseFuncOrString(vars[p], tween, index, target, targets);\n    }\n\n    return copy;\n  },\n      _checkPlugin = function _checkPlugin(property, vars, tween, index, target, targets) {\n    var plugin, pt, ptLookup, i;\n\n    if (_plugins[property] && (plugin = new _plugins[property]()).init(target, plugin.rawVars ? vars[property] : _processVars(vars[property], index, target, targets, tween), tween, index, targets) !== false) {\n      tween._pt = pt = new PropTween(tween._pt, target, property, 0, 1, plugin.render, plugin, 0, plugin.priority);\n\n      if (tween !== _quickTween) {\n        ptLookup = tween._ptLookup[tween._targets.indexOf(target)];\n        i = plugin._props.length;\n\n        while (i--) {\n          ptLookup[plugin._props[i]] = pt;\n        }\n      }\n    }\n\n    return plugin;\n  },\n      _overwritingTween,\n      _forceAllPropTweens,\n      _initTween = function _initTween(tween, time, tTime) {\n    var vars = tween.vars,\n        ease = vars.ease,\n        startAt = vars.startAt,\n        immediateRender = vars.immediateRender,\n        lazy = vars.lazy,\n        onUpdate = vars.onUpdate,\n        runBackwards = vars.runBackwards,\n        yoyoEase = vars.yoyoEase,\n        keyframes = vars.keyframes,\n        autoRevert = vars.autoRevert,\n        dur = tween._dur,\n        prevStartAt = tween._startAt,\n        targets = tween._targets,\n        parent = tween.parent,\n        fullTargets = parent && parent.data === \"nested\" ? parent.vars.targets : targets,\n        autoOverwrite = tween._overwrite === \"auto\" && !_suppressOverwrites,\n        tl = tween.timeline,\n        cleanVars,\n        i,\n        p,\n        pt,\n        target,\n        hasPriority,\n        gsData,\n        harness,\n        plugin,\n        ptLookup,\n        index,\n        harnessVars,\n        overwritten;\n    tl && (!keyframes || !ease) && (ease = \"none\");\n    tween._ease = _parseEase(ease, _defaults.ease);\n    tween._yEase = yoyoEase ? _invertEase(_parseEase(yoyoEase === true ? ease : yoyoEase, _defaults.ease)) : 0;\n\n    if (yoyoEase && tween._yoyo && !tween._repeat) {\n      yoyoEase = tween._yEase;\n      tween._yEase = tween._ease;\n      tween._ease = yoyoEase;\n    }\n\n    tween._from = !tl && !!vars.runBackwards;\n\n    if (!tl || keyframes && !vars.stagger) {\n      harness = targets[0] ? _getCache(targets[0]).harness : 0;\n      harnessVars = harness && vars[harness.prop];\n      cleanVars = _copyExcluding(vars, _reservedProps);\n\n      if (prevStartAt) {\n        prevStartAt._zTime < 0 && prevStartAt.progress(1);\n        time < 0 && runBackwards && immediateRender && !autoRevert ? prevStartAt.render(-1, true) : prevStartAt.revert(runBackwards && dur ? _revertConfigNoKill : _startAtRevertConfig);\n        prevStartAt._lazy = 0;\n      }\n\n      if (startAt) {\n        _removeFromParent(tween._startAt = Tween.set(targets, _setDefaults({\n          data: \"isStart\",\n          overwrite: false,\n          parent: parent,\n          immediateRender: true,\n          lazy: !prevStartAt && _isNotFalse(lazy),\n          startAt: null,\n          delay: 0,\n          onUpdate: onUpdate && function () {\n            return _callback(tween, \"onUpdate\");\n          },\n          stagger: 0\n        }, startAt)));\n\n        tween._startAt._dp = 0;\n        tween._startAt._sat = tween;\n        time < 0 && (_reverting || !immediateRender && !autoRevert) && tween._startAt.revert(_revertConfigNoKill);\n\n        if (immediateRender) {\n          if (dur && time <= 0 && tTime <= 0) {\n            time && (tween._zTime = time);\n            return;\n          }\n        }\n      } else if (runBackwards && dur) {\n        if (!prevStartAt) {\n          time && (immediateRender = false);\n          p = _setDefaults({\n            overwrite: false,\n            data: \"isFromStart\",\n            lazy: immediateRender && !prevStartAt && _isNotFalse(lazy),\n            immediateRender: immediateRender,\n            stagger: 0,\n            parent: parent\n          }, cleanVars);\n          harnessVars && (p[harness.prop] = harnessVars);\n\n          _removeFromParent(tween._startAt = Tween.set(targets, p));\n\n          tween._startAt._dp = 0;\n          tween._startAt._sat = tween;\n          time < 0 && (_reverting ? tween._startAt.revert(_revertConfigNoKill) : tween._startAt.render(-1, true));\n          tween._zTime = time;\n\n          if (!immediateRender) {\n            _initTween(tween._startAt, _tinyNum, _tinyNum);\n          } else if (!time) {\n            return;\n          }\n        }\n      }\n\n      tween._pt = tween._ptCache = 0;\n      lazy = dur && _isNotFalse(lazy) || lazy && !dur;\n\n      for (i = 0; i < targets.length; i++) {\n        target = targets[i];\n        gsData = target._gsap || _harness(targets)[i]._gsap;\n        tween._ptLookup[i] = ptLookup = {};\n        _lazyLookup[gsData.id] && _lazyTweens.length && _lazyRender();\n        index = fullTargets === targets ? i : fullTargets.indexOf(target);\n\n        if (harness && (plugin = new harness()).init(target, harnessVars || cleanVars, tween, index, fullTargets) !== false) {\n          tween._pt = pt = new PropTween(tween._pt, target, plugin.name, 0, 1, plugin.render, plugin, 0, plugin.priority);\n\n          plugin._props.forEach(function (name) {\n            ptLookup[name] = pt;\n          });\n\n          plugin.priority && (hasPriority = 1);\n        }\n\n        if (!harness || harnessVars) {\n          for (p in cleanVars) {\n            if (_plugins[p] && (plugin = _checkPlugin(p, cleanVars, tween, index, target, fullTargets))) {\n              plugin.priority && (hasPriority = 1);\n            } else {\n              ptLookup[p] = pt = _addPropTween.call(tween, target, p, \"get\", cleanVars[p], index, fullTargets, 0, vars.stringFilter);\n            }\n          }\n        }\n\n        tween._op && tween._op[i] && tween.kill(target, tween._op[i]);\n\n        if (autoOverwrite && tween._pt) {\n          _overwritingTween = tween;\n\n          _globalTimeline.killTweensOf(target, ptLookup, tween.globalTime(time));\n\n          overwritten = !tween.parent;\n          _overwritingTween = 0;\n        }\n\n        tween._pt && lazy && (_lazyLookup[gsData.id] = 1);\n      }\n\n      hasPriority && _sortPropTweensByPriority(tween);\n      tween._onInit && tween._onInit(tween);\n    }\n\n    tween._onUpdate = onUpdate;\n    tween._initted = (!tween._op || tween._pt) && !overwritten;\n    keyframes && time <= 0 && tl.render(_bigNum, true, true);\n  },\n      _updatePropTweens = function _updatePropTweens(tween, property, value, start, startIsRelative, ratio, time, skipRecursion) {\n    var ptCache = (tween._pt && tween._ptCache || (tween._ptCache = {}))[property],\n        pt,\n        rootPT,\n        lookup,\n        i;\n\n    if (!ptCache) {\n      ptCache = tween._ptCache[property] = [];\n      lookup = tween._ptLookup;\n      i = tween._targets.length;\n\n      while (i--) {\n        pt = lookup[i][property];\n\n        if (pt && pt.d && pt.d._pt) {\n          pt = pt.d._pt;\n\n          while (pt && pt.p !== property && pt.fp !== property) {\n            pt = pt._next;\n          }\n        }\n\n        if (!pt) {\n          _forceAllPropTweens = 1;\n          tween.vars[property] = \"+=0\";\n\n          _initTween(tween, time);\n\n          _forceAllPropTweens = 0;\n          return skipRecursion ? _warn(property + \" not eligible for reset\") : 1;\n        }\n\n        ptCache.push(pt);\n      }\n    }\n\n    i = ptCache.length;\n\n    while (i--) {\n      rootPT = ptCache[i];\n      pt = rootPT._pt || rootPT;\n      pt.s = (start || start === 0) && !startIsRelative ? start : pt.s + (start || 0) + ratio * pt.c;\n      pt.c = value - pt.s;\n      rootPT.e && (rootPT.e = _round(value) + getUnit(rootPT.e));\n      rootPT.b && (rootPT.b = pt.s + getUnit(rootPT.b));\n    }\n  },\n      _addAliasesToVars = function _addAliasesToVars(targets, vars) {\n    var harness = targets[0] ? _getCache(targets[0]).harness : 0,\n        propertyAliases = harness && harness.aliases,\n        copy,\n        p,\n        i,\n        aliases;\n\n    if (!propertyAliases) {\n      return vars;\n    }\n\n    copy = _merge({}, vars);\n\n    for (p in propertyAliases) {\n      if (p in copy) {\n        aliases = propertyAliases[p].split(\",\");\n        i = aliases.length;\n\n        while (i--) {\n          copy[aliases[i]] = copy[p];\n        }\n      }\n    }\n\n    return copy;\n  },\n      _parseKeyframe = function _parseKeyframe(prop, obj, allProps, easeEach) {\n    var ease = obj.ease || easeEach || \"power1.inOut\",\n        p,\n        a;\n\n    if (_isArray(obj)) {\n      a = allProps[prop] || (allProps[prop] = []);\n      obj.forEach(function (value, i) {\n        return a.push({\n          t: i / (obj.length - 1) * 100,\n          v: value,\n          e: ease\n        });\n      });\n    } else {\n      for (p in obj) {\n        a = allProps[p] || (allProps[p] = []);\n        p === \"ease\" || a.push({\n          t: parseFloat(prop),\n          v: obj[p],\n          e: ease\n        });\n      }\n    }\n  },\n      _parseFuncOrString = function _parseFuncOrString(value, tween, i, target, targets) {\n    return _isFunction(value) ? value.call(tween, i, target, targets) : _isString(value) && ~value.indexOf(\"random(\") ? _replaceRandom(value) : value;\n  },\n      _staggerTweenProps = _callbackNames + \"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert\",\n      _staggerPropsToSkip = {};\n\n  _forEachName(_staggerTweenProps + \",id,stagger,delay,duration,paused,scrollTrigger\", function (name) {\n    return _staggerPropsToSkip[name] = 1;\n  });\n\n  var Tween = function (_Animation2) {\n    _inheritsLoose(Tween, _Animation2);\n\n    function Tween(targets, vars, position, skipInherit) {\n      var _this3;\n\n      if (typeof vars === \"number\") {\n        position.duration = vars;\n        vars = position;\n        position = null;\n      }\n\n      _this3 = _Animation2.call(this, skipInherit ? vars : _inheritDefaults(vars)) || this;\n      var _this3$vars = _this3.vars,\n          duration = _this3$vars.duration,\n          delay = _this3$vars.delay,\n          immediateRender = _this3$vars.immediateRender,\n          stagger = _this3$vars.stagger,\n          overwrite = _this3$vars.overwrite,\n          keyframes = _this3$vars.keyframes,\n          defaults = _this3$vars.defaults,\n          scrollTrigger = _this3$vars.scrollTrigger,\n          yoyoEase = _this3$vars.yoyoEase,\n          parent = vars.parent || _globalTimeline,\n          parsedTargets = (_isArray(targets) || _isTypedArray(targets) ? _isNumber(targets[0]) : \"length\" in vars) ? [targets] : toArray(targets),\n          tl,\n          i,\n          copy,\n          l,\n          p,\n          curTarget,\n          staggerFunc,\n          staggerVarsToMerge;\n      _this3._targets = parsedTargets.length ? _harness(parsedTargets) : _warn(\"GSAP target \" + targets + \" not found. https://gsap.com\", !_config.nullTargetWarn) || [];\n      _this3._ptLookup = [];\n      _this3._overwrite = overwrite;\n\n      if (keyframes || stagger || _isFuncOrString(duration) || _isFuncOrString(delay)) {\n        vars = _this3.vars;\n        tl = _this3.timeline = new Timeline({\n          data: \"nested\",\n          defaults: defaults || {},\n          targets: parent && parent.data === \"nested\" ? parent.vars.targets : parsedTargets\n        });\n        tl.kill();\n        tl.parent = tl._dp = _assertThisInitialized(_this3);\n        tl._start = 0;\n\n        if (stagger || _isFuncOrString(duration) || _isFuncOrString(delay)) {\n          l = parsedTargets.length;\n          staggerFunc = stagger && distribute(stagger);\n\n          if (_isObject(stagger)) {\n            for (p in stagger) {\n              if (~_staggerTweenProps.indexOf(p)) {\n                staggerVarsToMerge || (staggerVarsToMerge = {});\n                staggerVarsToMerge[p] = stagger[p];\n              }\n            }\n          }\n\n          for (i = 0; i < l; i++) {\n            copy = _copyExcluding(vars, _staggerPropsToSkip);\n            copy.stagger = 0;\n            yoyoEase && (copy.yoyoEase = yoyoEase);\n            staggerVarsToMerge && _merge(copy, staggerVarsToMerge);\n            curTarget = parsedTargets[i];\n            copy.duration = +_parseFuncOrString(duration, _assertThisInitialized(_this3), i, curTarget, parsedTargets);\n            copy.delay = (+_parseFuncOrString(delay, _assertThisInitialized(_this3), i, curTarget, parsedTargets) || 0) - _this3._delay;\n\n            if (!stagger && l === 1 && copy.delay) {\n              _this3._delay = delay = copy.delay;\n              _this3._start += delay;\n              copy.delay = 0;\n            }\n\n            tl.to(curTarget, copy, staggerFunc ? staggerFunc(i, curTarget, parsedTargets) : 0);\n            tl._ease = _easeMap.none;\n          }\n\n          tl.duration() ? duration = delay = 0 : _this3.timeline = 0;\n        } else if (keyframes) {\n          _inheritDefaults(_setDefaults(tl.vars.defaults, {\n            ease: \"none\"\n          }));\n\n          tl._ease = _parseEase(keyframes.ease || vars.ease || \"none\");\n          var time = 0,\n              a,\n              kf,\n              v;\n\n          if (_isArray(keyframes)) {\n            keyframes.forEach(function (frame) {\n              return tl.to(parsedTargets, frame, \">\");\n            });\n            tl.duration();\n          } else {\n            copy = {};\n\n            for (p in keyframes) {\n              p === \"ease\" || p === \"easeEach\" || _parseKeyframe(p, keyframes[p], copy, keyframes.easeEach);\n            }\n\n            for (p in copy) {\n              a = copy[p].sort(function (a, b) {\n                return a.t - b.t;\n              });\n              time = 0;\n\n              for (i = 0; i < a.length; i++) {\n                kf = a[i];\n                v = {\n                  ease: kf.e,\n                  duration: (kf.t - (i ? a[i - 1].t : 0)) / 100 * duration\n                };\n                v[p] = kf.v;\n                tl.to(parsedTargets, v, time);\n                time += v.duration;\n              }\n            }\n\n            tl.duration() < duration && tl.to({}, {\n              duration: duration - tl.duration()\n            });\n          }\n        }\n\n        duration || _this3.duration(duration = tl.duration());\n      } else {\n        _this3.timeline = 0;\n      }\n\n      if (overwrite === true && !_suppressOverwrites) {\n        _overwritingTween = _assertThisInitialized(_this3);\n\n        _globalTimeline.killTweensOf(parsedTargets);\n\n        _overwritingTween = 0;\n      }\n\n      _addToTimeline(parent, _assertThisInitialized(_this3), position);\n\n      vars.reversed && _this3.reverse();\n      vars.paused && _this3.paused(true);\n\n      if (immediateRender || !duration && !keyframes && _this3._start === _roundPrecise(parent._time) && _isNotFalse(immediateRender) && _hasNoPausedAncestors(_assertThisInitialized(_this3)) && parent.data !== \"nested\") {\n        _this3._tTime = -_tinyNum;\n\n        _this3.render(Math.max(0, -delay) || 0);\n      }\n\n      scrollTrigger && _scrollTrigger(_assertThisInitialized(_this3), scrollTrigger);\n      return _this3;\n    }\n\n    var _proto3 = Tween.prototype;\n\n    _proto3.render = function render(totalTime, suppressEvents, force) {\n      var prevTime = this._time,\n          tDur = this._tDur,\n          dur = this._dur,\n          isNegative = totalTime < 0,\n          tTime = totalTime > tDur - _tinyNum && !isNegative ? tDur : totalTime < _tinyNum ? 0 : totalTime,\n          time,\n          pt,\n          iteration,\n          cycleDuration,\n          prevIteration,\n          isYoyo,\n          ratio,\n          timeline,\n          yoyoEase;\n\n      if (!dur) {\n        _renderZeroDurationTween(this, totalTime, suppressEvents, force);\n      } else if (tTime !== this._tTime || !totalTime || force || !this._initted && this._tTime || this._startAt && this._zTime < 0 !== isNegative || this._lazy) {\n        time = tTime;\n        timeline = this.timeline;\n\n        if (this._repeat) {\n          cycleDuration = dur + this._rDelay;\n\n          if (this._repeat < -1 && isNegative) {\n            return this.totalTime(cycleDuration * 100 + totalTime, suppressEvents, force);\n          }\n\n          time = _roundPrecise(tTime % cycleDuration);\n\n          if (tTime === tDur) {\n            iteration = this._repeat;\n            time = dur;\n          } else {\n            prevIteration = _roundPrecise(tTime / cycleDuration);\n            iteration = ~~prevIteration;\n\n            if (iteration && iteration === prevIteration) {\n              time = dur;\n              iteration--;\n            } else if (time > dur) {\n              time = dur;\n            }\n          }\n\n          isYoyo = this._yoyo && iteration & 1;\n\n          if (isYoyo) {\n            yoyoEase = this._yEase;\n            time = dur - time;\n          }\n\n          prevIteration = _animationCycle(this._tTime, cycleDuration);\n\n          if (time === prevTime && !force && this._initted && iteration === prevIteration) {\n            this._tTime = tTime;\n            return this;\n          }\n\n          if (iteration !== prevIteration) {\n            timeline && this._yEase && _propagateYoyoEase(timeline, isYoyo);\n\n            if (this.vars.repeatRefresh && !isYoyo && !this._lock && time !== cycleDuration && this._initted) {\n              this._lock = force = 1;\n              this.render(_roundPrecise(cycleDuration * iteration), true).invalidate()._lock = 0;\n            }\n          }\n        }\n\n        if (!this._initted) {\n          if (_attemptInitTween(this, isNegative ? totalTime : time, force, suppressEvents, tTime)) {\n            this._tTime = 0;\n            return this;\n          }\n\n          if (prevTime !== this._time && !(force && this.vars.repeatRefresh && iteration !== prevIteration)) {\n            return this;\n          }\n\n          if (dur !== this._dur) {\n            return this.render(totalTime, suppressEvents, force);\n          }\n        }\n\n        this._tTime = tTime;\n        this._time = time;\n\n        if (!this._act && this._ts) {\n          this._act = 1;\n          this._lazy = 0;\n        }\n\n        this.ratio = ratio = (yoyoEase || this._ease)(time / dur);\n\n        if (this._from) {\n          this.ratio = ratio = 1 - ratio;\n        }\n\n        if (!prevTime && tTime && !suppressEvents && !prevIteration) {\n          _callback(this, \"onStart\");\n\n          if (this._tTime !== tTime) {\n            return this;\n          }\n        }\n\n        pt = this._pt;\n\n        while (pt) {\n          pt.r(ratio, pt.d);\n          pt = pt._next;\n        }\n\n        timeline && timeline.render(totalTime < 0 ? totalTime : timeline._dur * timeline._ease(time / this._dur), suppressEvents, force) || this._startAt && (this._zTime = totalTime);\n\n        if (this._onUpdate && !suppressEvents) {\n          isNegative && _rewindStartAt(this, totalTime, suppressEvents, force);\n\n          _callback(this, \"onUpdate\");\n        }\n\n        this._repeat && iteration !== prevIteration && this.vars.onRepeat && !suppressEvents && this.parent && _callback(this, \"onRepeat\");\n\n        if ((tTime === this._tDur || !tTime) && this._tTime === tTime) {\n          isNegative && !this._onUpdate && _rewindStartAt(this, totalTime, true, true);\n          (totalTime || !dur) && (tTime === this._tDur && this._ts > 0 || !tTime && this._ts < 0) && _removeFromParent(this, 1);\n\n          if (!suppressEvents && !(isNegative && !prevTime) && (tTime || prevTime || isYoyo)) {\n            _callback(this, tTime === tDur ? \"onComplete\" : \"onReverseComplete\", true);\n\n            this._prom && !(tTime < tDur && this.timeScale() > 0) && this._prom();\n          }\n        }\n      }\n\n      return this;\n    };\n\n    _proto3.targets = function targets() {\n      return this._targets;\n    };\n\n    _proto3.invalidate = function invalidate(soft) {\n      (!soft || !this.vars.runBackwards) && (this._startAt = 0);\n      this._pt = this._op = this._onUpdate = this._lazy = this.ratio = 0;\n      this._ptLookup = [];\n      this.timeline && this.timeline.invalidate(soft);\n      return _Animation2.prototype.invalidate.call(this, soft);\n    };\n\n    _proto3.resetTo = function resetTo(property, value, start, startIsRelative, skipRecursion) {\n      _tickerActive || _ticker.wake();\n      this._ts || this.play();\n      var time = Math.min(this._dur, (this._dp._time - this._start) * this._ts),\n          ratio;\n      this._initted || _initTween(this, time);\n      ratio = this._ease(time / this._dur);\n\n      if (_updatePropTweens(this, property, value, start, startIsRelative, ratio, time, skipRecursion)) {\n        return this.resetTo(property, value, start, startIsRelative, 1);\n      }\n\n      _alignPlayhead(this, 0);\n\n      this.parent || _addLinkedListItem(this._dp, this, \"_first\", \"_last\", this._dp._sort ? \"_start\" : 0);\n      return this.render(0);\n    };\n\n    _proto3.kill = function kill(targets, vars) {\n      if (vars === void 0) {\n        vars = \"all\";\n      }\n\n      if (!targets && (!vars || vars === \"all\")) {\n        this._lazy = this._pt = 0;\n        this.parent ? _interrupt(this) : this.scrollTrigger && this.scrollTrigger.kill(!!_reverting);\n        return this;\n      }\n\n      if (this.timeline) {\n        var tDur = this.timeline.totalDuration();\n        this.timeline.killTweensOf(targets, vars, _overwritingTween && _overwritingTween.vars.overwrite !== true)._first || _interrupt(this);\n        this.parent && tDur !== this.timeline.totalDuration() && _setDuration(this, this._dur * this.timeline._tDur / tDur, 0, 1);\n        return this;\n      }\n\n      var parsedTargets = this._targets,\n          killingTargets = targets ? toArray(targets) : parsedTargets,\n          propTweenLookup = this._ptLookup,\n          firstPT = this._pt,\n          overwrittenProps,\n          curLookup,\n          curOverwriteProps,\n          props,\n          p,\n          pt,\n          i;\n\n      if ((!vars || vars === \"all\") && _arraysMatch(parsedTargets, killingTargets)) {\n        vars === \"all\" && (this._pt = 0);\n        return _interrupt(this);\n      }\n\n      overwrittenProps = this._op = this._op || [];\n\n      if (vars !== \"all\") {\n        if (_isString(vars)) {\n          p = {};\n\n          _forEachName(vars, function (name) {\n            return p[name] = 1;\n          });\n\n          vars = p;\n        }\n\n        vars = _addAliasesToVars(parsedTargets, vars);\n      }\n\n      i = parsedTargets.length;\n\n      while (i--) {\n        if (~killingTargets.indexOf(parsedTargets[i])) {\n          curLookup = propTweenLookup[i];\n\n          if (vars === \"all\") {\n            overwrittenProps[i] = vars;\n            props = curLookup;\n            curOverwriteProps = {};\n          } else {\n            curOverwriteProps = overwrittenProps[i] = overwrittenProps[i] || {};\n            props = vars;\n          }\n\n          for (p in props) {\n            pt = curLookup && curLookup[p];\n\n            if (pt) {\n              if (!(\"kill\" in pt.d) || pt.d.kill(p) === true) {\n                _removeLinkedListItem(this, pt, \"_pt\");\n              }\n\n              delete curLookup[p];\n            }\n\n            if (curOverwriteProps !== \"all\") {\n              curOverwriteProps[p] = 1;\n            }\n          }\n        }\n      }\n\n      this._initted && !this._pt && firstPT && _interrupt(this);\n      return this;\n    };\n\n    Tween.to = function to(targets, vars) {\n      return new Tween(targets, vars, arguments[2]);\n    };\n\n    Tween.from = function from(targets, vars) {\n      return _createTweenType(1, arguments);\n    };\n\n    Tween.delayedCall = function delayedCall(delay, callback, params, scope) {\n      return new Tween(callback, 0, {\n        immediateRender: false,\n        lazy: false,\n        overwrite: false,\n        delay: delay,\n        onComplete: callback,\n        onReverseComplete: callback,\n        onCompleteParams: params,\n        onReverseCompleteParams: params,\n        callbackScope: scope\n      });\n    };\n\n    Tween.fromTo = function fromTo(targets, fromVars, toVars) {\n      return _createTweenType(2, arguments);\n    };\n\n    Tween.set = function set(targets, vars) {\n      vars.duration = 0;\n      vars.repeatDelay || (vars.repeat = 0);\n      return new Tween(targets, vars);\n    };\n\n    Tween.killTweensOf = function killTweensOf(targets, props, onlyActive) {\n      return _globalTimeline.killTweensOf(targets, props, onlyActive);\n    };\n\n    return Tween;\n  }(Animation);\n\n  _setDefaults(Tween.prototype, {\n    _targets: [],\n    _lazy: 0,\n    _startAt: 0,\n    _op: 0,\n    _onInit: 0\n  });\n\n  _forEachName(\"staggerTo,staggerFrom,staggerFromTo\", function (name) {\n    Tween[name] = function () {\n      var tl = new Timeline(),\n          params = _slice.call(arguments, 0);\n\n      params.splice(name === \"staggerFromTo\" ? 5 : 4, 0, 0);\n      return tl[name].apply(tl, params);\n    };\n  });\n\n  var _setterPlain = function _setterPlain(target, property, value) {\n    return target[property] = value;\n  },\n      _setterFunc = function _setterFunc(target, property, value) {\n    return target[property](value);\n  },\n      _setterFuncWithParam = function _setterFuncWithParam(target, property, value, data) {\n    return target[property](data.fp, value);\n  },\n      _setterAttribute = function _setterAttribute(target, property, value) {\n    return target.setAttribute(property, value);\n  },\n      _getSetter = function _getSetter(target, property) {\n    return _isFunction(target[property]) ? _setterFunc : _isUndefined(target[property]) && target.setAttribute ? _setterAttribute : _setterPlain;\n  },\n      _renderPlain = function _renderPlain(ratio, data) {\n    return data.set(data.t, data.p, Math.round((data.s + data.c * ratio) * 1000000) / 1000000, data);\n  },\n      _renderBoolean = function _renderBoolean(ratio, data) {\n    return data.set(data.t, data.p, !!(data.s + data.c * ratio), data);\n  },\n      _renderComplexString = function _renderComplexString(ratio, data) {\n    var pt = data._pt,\n        s = \"\";\n\n    if (!ratio && data.b) {\n      s = data.b;\n    } else if (ratio === 1 && data.e) {\n      s = data.e;\n    } else {\n      while (pt) {\n        s = pt.p + (pt.m ? pt.m(pt.s + pt.c * ratio) : Math.round((pt.s + pt.c * ratio) * 10000) / 10000) + s;\n        pt = pt._next;\n      }\n\n      s += data.c;\n    }\n\n    data.set(data.t, data.p, s, data);\n  },\n      _renderPropTweens = function _renderPropTweens(ratio, data) {\n    var pt = data._pt;\n\n    while (pt) {\n      pt.r(ratio, pt.d);\n      pt = pt._next;\n    }\n  },\n      _addPluginModifier = function _addPluginModifier(modifier, tween, target, property) {\n    var pt = this._pt,\n        next;\n\n    while (pt) {\n      next = pt._next;\n      pt.p === property && pt.modifier(modifier, tween, target);\n      pt = next;\n    }\n  },\n      _killPropTweensOf = function _killPropTweensOf(property) {\n    var pt = this._pt,\n        hasNonDependentRemaining,\n        next;\n\n    while (pt) {\n      next = pt._next;\n\n      if (pt.p === property && !pt.op || pt.op === property) {\n        _removeLinkedListItem(this, pt, \"_pt\");\n      } else if (!pt.dep) {\n        hasNonDependentRemaining = 1;\n      }\n\n      pt = next;\n    }\n\n    return !hasNonDependentRemaining;\n  },\n      _setterWithModifier = function _setterWithModifier(target, property, value, data) {\n    data.mSet(target, property, data.m.call(data.tween, value, data.mt), data);\n  },\n      _sortPropTweensByPriority = function _sortPropTweensByPriority(parent) {\n    var pt = parent._pt,\n        next,\n        pt2,\n        first,\n        last;\n\n    while (pt) {\n      next = pt._next;\n      pt2 = first;\n\n      while (pt2 && pt2.pr > pt.pr) {\n        pt2 = pt2._next;\n      }\n\n      if (pt._prev = pt2 ? pt2._prev : last) {\n        pt._prev._next = pt;\n      } else {\n        first = pt;\n      }\n\n      if (pt._next = pt2) {\n        pt2._prev = pt;\n      } else {\n        last = pt;\n      }\n\n      pt = next;\n    }\n\n    parent._pt = first;\n  };\n\n  var PropTween = function () {\n    function PropTween(next, target, prop, start, change, renderer, data, setter, priority) {\n      this.t = target;\n      this.s = start;\n      this.c = change;\n      this.p = prop;\n      this.r = renderer || _renderPlain;\n      this.d = data || this;\n      this.set = setter || _setterPlain;\n      this.pr = priority || 0;\n      this._next = next;\n\n      if (next) {\n        next._prev = this;\n      }\n    }\n\n    var _proto4 = PropTween.prototype;\n\n    _proto4.modifier = function modifier(func, tween, target) {\n      this.mSet = this.mSet || this.set;\n      this.set = _setterWithModifier;\n      this.m = func;\n      this.mt = target;\n      this.tween = tween;\n    };\n\n    return PropTween;\n  }();\n\n  _forEachName(_callbackNames + \"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger\", function (name) {\n    return _reservedProps[name] = 1;\n  });\n\n  _globals.TweenMax = _globals.TweenLite = Tween;\n  _globals.TimelineLite = _globals.TimelineMax = Timeline;\n  _globalTimeline = new Timeline({\n    sortChildren: false,\n    defaults: _defaults,\n    autoRemoveChildren: true,\n    id: \"root\",\n    smoothChildTiming: true\n  });\n  _config.stringFilter = _colorStringFilter;\n\n  var _media = [],\n      _listeners = {},\n      _emptyArray = [],\n      _lastMediaTime = 0,\n      _contextID = 0,\n      _dispatch = function _dispatch(type) {\n    return (_listeners[type] || _emptyArray).map(function (f) {\n      return f();\n    });\n  },\n      _onMediaChange = function _onMediaChange() {\n    var time = Date.now(),\n        matches = [];\n\n    if (time - _lastMediaTime > 2) {\n      _dispatch(\"matchMediaInit\");\n\n      _media.forEach(function (c) {\n        var queries = c.queries,\n            conditions = c.conditions,\n            match,\n            p,\n            anyMatch,\n            toggled;\n\n        for (p in queries) {\n          match = _win.matchMedia(queries[p]).matches;\n          match && (anyMatch = 1);\n\n          if (match !== conditions[p]) {\n            conditions[p] = match;\n            toggled = 1;\n          }\n        }\n\n        if (toggled) {\n          c.revert();\n          anyMatch && matches.push(c);\n        }\n      });\n\n      _dispatch(\"matchMediaRevert\");\n\n      matches.forEach(function (c) {\n        return c.onMatch(c, function (func) {\n          return c.add(null, func);\n        });\n      });\n      _lastMediaTime = time;\n\n      _dispatch(\"matchMedia\");\n    }\n  };\n\n  var Context = function () {\n    function Context(func, scope) {\n      this.selector = scope && selector(scope);\n      this.data = [];\n      this._r = [];\n      this.isReverted = false;\n      this.id = _contextID++;\n      func && this.add(func);\n    }\n\n    var _proto5 = Context.prototype;\n\n    _proto5.add = function add(name, func, scope) {\n      if (_isFunction(name)) {\n        scope = func;\n        func = name;\n        name = _isFunction;\n      }\n\n      var self = this,\n          f = function f() {\n        var prev = _context,\n            prevSelector = self.selector,\n            result;\n        prev && prev !== self && prev.data.push(self);\n        scope && (self.selector = selector(scope));\n        _context = self;\n        result = func.apply(self, arguments);\n        _isFunction(result) && self._r.push(result);\n        _context = prev;\n        self.selector = prevSelector;\n        self.isReverted = false;\n        return result;\n      };\n\n      self.last = f;\n      return name === _isFunction ? f(self, function (func) {\n        return self.add(null, func);\n      }) : name ? self[name] = f : f;\n    };\n\n    _proto5.ignore = function ignore(func) {\n      var prev = _context;\n      _context = null;\n      func(this);\n      _context = prev;\n    };\n\n    _proto5.getTweens = function getTweens() {\n      var a = [];\n      this.data.forEach(function (e) {\n        return e instanceof Context ? a.push.apply(a, e.getTweens()) : e instanceof Tween && !(e.parent && e.parent.data === \"nested\") && a.push(e);\n      });\n      return a;\n    };\n\n    _proto5.clear = function clear() {\n      this._r.length = this.data.length = 0;\n    };\n\n    _proto5.kill = function kill(revert, matchMedia) {\n      var _this4 = this;\n\n      if (revert) {\n        (function () {\n          var tweens = _this4.getTweens(),\n              i = _this4.data.length,\n              t;\n\n          while (i--) {\n            t = _this4.data[i];\n\n            if (t.data === \"isFlip\") {\n              t.revert();\n              t.getChildren(true, true, false).forEach(function (tween) {\n                return tweens.splice(tweens.indexOf(tween), 1);\n              });\n            }\n          }\n\n          tweens.map(function (t) {\n            return {\n              g: t._dur || t._delay || t._sat && !t._sat.vars.immediateRender ? t.globalTime(0) : -Infinity,\n              t: t\n            };\n          }).sort(function (a, b) {\n            return b.g - a.g || -Infinity;\n          }).forEach(function (o) {\n            return o.t.revert(revert);\n          });\n          i = _this4.data.length;\n\n          while (i--) {\n            t = _this4.data[i];\n\n            if (t instanceof Timeline) {\n              if (t.data !== \"nested\") {\n                t.scrollTrigger && t.scrollTrigger.revert();\n                t.kill();\n              }\n            } else {\n              !(t instanceof Tween) && t.revert && t.revert(revert);\n            }\n          }\n\n          _this4._r.forEach(function (f) {\n            return f(revert, _this4);\n          });\n\n          _this4.isReverted = true;\n        })();\n      } else {\n        this.data.forEach(function (e) {\n          return e.kill && e.kill();\n        });\n      }\n\n      this.clear();\n\n      if (matchMedia) {\n        var i = _media.length;\n\n        while (i--) {\n          _media[i].id === this.id && _media.splice(i, 1);\n        }\n      }\n    };\n\n    _proto5.revert = function revert(config) {\n      this.kill(config || {});\n    };\n\n    return Context;\n  }();\n\n  var MatchMedia = function () {\n    function MatchMedia(scope) {\n      this.contexts = [];\n      this.scope = scope;\n      _context && _context.data.push(this);\n    }\n\n    var _proto6 = MatchMedia.prototype;\n\n    _proto6.add = function add(conditions, func, scope) {\n      _isObject(conditions) || (conditions = {\n        matches: conditions\n      });\n      var context = new Context(0, scope || this.scope),\n          cond = context.conditions = {},\n          mq,\n          p,\n          active;\n      _context && !context.selector && (context.selector = _context.selector);\n      this.contexts.push(context);\n      func = context.add(\"onMatch\", func);\n      context.queries = conditions;\n\n      for (p in conditions) {\n        if (p === \"all\") {\n          active = 1;\n        } else {\n          mq = _win.matchMedia(conditions[p]);\n\n          if (mq) {\n            _media.indexOf(context) < 0 && _media.push(context);\n            (cond[p] = mq.matches) && (active = 1);\n            mq.addListener ? mq.addListener(_onMediaChange) : mq.addEventListener(\"change\", _onMediaChange);\n          }\n        }\n      }\n\n      active && func(context, function (f) {\n        return context.add(null, f);\n      });\n      return this;\n    };\n\n    _proto6.revert = function revert(config) {\n      this.kill(config || {});\n    };\n\n    _proto6.kill = function kill(revert) {\n      this.contexts.forEach(function (c) {\n        return c.kill(revert, true);\n      });\n    };\n\n    return MatchMedia;\n  }();\n\n  var _gsap = {\n    registerPlugin: function registerPlugin() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      args.forEach(function (config) {\n        return _createPlugin(config);\n      });\n    },\n    timeline: function timeline(vars) {\n      return new Timeline(vars);\n    },\n    getTweensOf: function getTweensOf(targets, onlyActive) {\n      return _globalTimeline.getTweensOf(targets, onlyActive);\n    },\n    getProperty: function getProperty(target, property, unit, uncache) {\n      _isString(target) && (target = toArray(target)[0]);\n\n      var getter = _getCache(target || {}).get,\n          format = unit ? _passThrough : _numericIfPossible;\n\n      unit === \"native\" && (unit = \"\");\n      return !target ? target : !property ? function (property, unit, uncache) {\n        return format((_plugins[property] && _plugins[property].get || getter)(target, property, unit, uncache));\n      } : format((_plugins[property] && _plugins[property].get || getter)(target, property, unit, uncache));\n    },\n    quickSetter: function quickSetter(target, property, unit) {\n      target = toArray(target);\n\n      if (target.length > 1) {\n        var setters = target.map(function (t) {\n          return gsap.quickSetter(t, property, unit);\n        }),\n            l = setters.length;\n        return function (value) {\n          var i = l;\n\n          while (i--) {\n            setters[i](value);\n          }\n        };\n      }\n\n      target = target[0] || {};\n\n      var Plugin = _plugins[property],\n          cache = _getCache(target),\n          p = cache.harness && (cache.harness.aliases || {})[property] || property,\n          setter = Plugin ? function (value) {\n        var p = new Plugin();\n        _quickTween._pt = 0;\n        p.init(target, unit ? value + unit : value, _quickTween, 0, [target]);\n        p.render(1, p);\n        _quickTween._pt && _renderPropTweens(1, _quickTween);\n      } : cache.set(target, p);\n\n      return Plugin ? setter : function (value) {\n        return setter(target, p, unit ? value + unit : value, cache, 1);\n      };\n    },\n    quickTo: function quickTo(target, property, vars) {\n      var _setDefaults2;\n\n      var tween = gsap.to(target, _setDefaults((_setDefaults2 = {}, _setDefaults2[property] = \"+=0.1\", _setDefaults2.paused = true, _setDefaults2.stagger = 0, _setDefaults2), vars || {})),\n          func = function func(value, start, startIsRelative) {\n        return tween.resetTo(property, value, start, startIsRelative);\n      };\n\n      func.tween = tween;\n      return func;\n    },\n    isTweening: function isTweening(targets) {\n      return _globalTimeline.getTweensOf(targets, true).length > 0;\n    },\n    defaults: function defaults(value) {\n      value && value.ease && (value.ease = _parseEase(value.ease, _defaults.ease));\n      return _mergeDeep(_defaults, value || {});\n    },\n    config: function config(value) {\n      return _mergeDeep(_config, value || {});\n    },\n    registerEffect: function registerEffect(_ref3) {\n      var name = _ref3.name,\n          effect = _ref3.effect,\n          plugins = _ref3.plugins,\n          defaults = _ref3.defaults,\n          extendTimeline = _ref3.extendTimeline;\n      (plugins || \"\").split(\",\").forEach(function (pluginName) {\n        return pluginName && !_plugins[pluginName] && !_globals[pluginName] && _warn(name + \" effect requires \" + pluginName + \" plugin.\");\n      });\n\n      _effects[name] = function (targets, vars, tl) {\n        return effect(toArray(targets), _setDefaults(vars || {}, defaults), tl);\n      };\n\n      if (extendTimeline) {\n        Timeline.prototype[name] = function (targets, vars, position) {\n          return this.add(_effects[name](targets, _isObject(vars) ? vars : (position = vars) && {}, this), position);\n        };\n      }\n    },\n    registerEase: function registerEase(name, ease) {\n      _easeMap[name] = _parseEase(ease);\n    },\n    parseEase: function parseEase(ease, defaultEase) {\n      return arguments.length ? _parseEase(ease, defaultEase) : _easeMap;\n    },\n    getById: function getById(id) {\n      return _globalTimeline.getById(id);\n    },\n    exportRoot: function exportRoot(vars, includeDelayedCalls) {\n      if (vars === void 0) {\n        vars = {};\n      }\n\n      var tl = new Timeline(vars),\n          child,\n          next;\n      tl.smoothChildTiming = _isNotFalse(vars.smoothChildTiming);\n\n      _globalTimeline.remove(tl);\n\n      tl._dp = 0;\n      tl._time = tl._tTime = _globalTimeline._time;\n      child = _globalTimeline._first;\n\n      while (child) {\n        next = child._next;\n\n        if (includeDelayedCalls || !(!child._dur && child instanceof Tween && child.vars.onComplete === child._targets[0])) {\n          _addToTimeline(tl, child, child._start - child._delay);\n        }\n\n        child = next;\n      }\n\n      _addToTimeline(_globalTimeline, tl, 0);\n\n      return tl;\n    },\n    context: function context(func, scope) {\n      return func ? new Context(func, scope) : _context;\n    },\n    matchMedia: function matchMedia(scope) {\n      return new MatchMedia(scope);\n    },\n    matchMediaRefresh: function matchMediaRefresh() {\n      return _media.forEach(function (c) {\n        var cond = c.conditions,\n            found,\n            p;\n\n        for (p in cond) {\n          if (cond[p]) {\n            cond[p] = false;\n            found = 1;\n          }\n        }\n\n        found && c.revert();\n      }) || _onMediaChange();\n    },\n    addEventListener: function addEventListener(type, callback) {\n      var a = _listeners[type] || (_listeners[type] = []);\n      ~a.indexOf(callback) || a.push(callback);\n    },\n    removeEventListener: function removeEventListener(type, callback) {\n      var a = _listeners[type],\n          i = a && a.indexOf(callback);\n      i >= 0 && a.splice(i, 1);\n    },\n    utils: {\n      wrap: wrap,\n      wrapYoyo: wrapYoyo,\n      distribute: distribute,\n      random: random,\n      snap: snap,\n      normalize: normalize,\n      getUnit: getUnit,\n      clamp: clamp,\n      splitColor: splitColor,\n      toArray: toArray,\n      selector: selector,\n      mapRange: mapRange,\n      pipe: pipe,\n      unitize: unitize,\n      interpolate: interpolate,\n      shuffle: shuffle\n    },\n    install: _install,\n    effects: _effects,\n    ticker: _ticker,\n    updateRoot: Timeline.updateRoot,\n    plugins: _plugins,\n    globalTimeline: _globalTimeline,\n    core: {\n      PropTween: PropTween,\n      globals: _addGlobal,\n      Tween: Tween,\n      Timeline: Timeline,\n      Animation: Animation,\n      getCache: _getCache,\n      _removeLinkedListItem: _removeLinkedListItem,\n      reverting: function reverting() {\n        return _reverting;\n      },\n      context: function context(toAdd) {\n        if (toAdd && _context) {\n          _context.data.push(toAdd);\n\n          toAdd._ctx = _context;\n        }\n\n        return _context;\n      },\n      suppressOverwrites: function suppressOverwrites(value) {\n        return _suppressOverwrites = value;\n      }\n    }\n  };\n\n  _forEachName(\"to,from,fromTo,delayedCall,set,killTweensOf\", function (name) {\n    return _gsap[name] = Tween[name];\n  });\n\n  _ticker.add(Timeline.updateRoot);\n\n  _quickTween = _gsap.to({}, {\n    duration: 0\n  });\n\n  var _getPluginPropTween = function _getPluginPropTween(plugin, prop) {\n    var pt = plugin._pt;\n\n    while (pt && pt.p !== prop && pt.op !== prop && pt.fp !== prop) {\n      pt = pt._next;\n    }\n\n    return pt;\n  },\n      _addModifiers = function _addModifiers(tween, modifiers) {\n    var targets = tween._targets,\n        p,\n        i,\n        pt;\n\n    for (p in modifiers) {\n      i = targets.length;\n\n      while (i--) {\n        pt = tween._ptLookup[i][p];\n\n        if (pt && (pt = pt.d)) {\n          if (pt._pt) {\n            pt = _getPluginPropTween(pt, p);\n          }\n\n          pt && pt.modifier && pt.modifier(modifiers[p], tween, targets[i], p);\n        }\n      }\n    }\n  },\n      _buildModifierPlugin = function _buildModifierPlugin(name, modifier) {\n    return {\n      name: name,\n      headless: 1,\n      rawVars: 1,\n      init: function init(target, vars, tween) {\n        tween._onInit = function (tween) {\n          var temp, p;\n\n          if (_isString(vars)) {\n            temp = {};\n\n            _forEachName(vars, function (name) {\n              return temp[name] = 1;\n            });\n\n            vars = temp;\n          }\n\n          if (modifier) {\n            temp = {};\n\n            for (p in vars) {\n              temp[p] = modifier(vars[p]);\n            }\n\n            vars = temp;\n          }\n\n          _addModifiers(tween, vars);\n        };\n      }\n    };\n  };\n\n  var gsap = _gsap.registerPlugin({\n    name: \"attr\",\n    init: function init(target, vars, tween, index, targets) {\n      var p, pt, v;\n      this.tween = tween;\n\n      for (p in vars) {\n        v = target.getAttribute(p) || \"\";\n        pt = this.add(target, \"setAttribute\", (v || 0) + \"\", vars[p], index, targets, 0, 0, p);\n        pt.op = p;\n        pt.b = v;\n\n        this._props.push(p);\n      }\n    },\n    render: function render(ratio, data) {\n      var pt = data._pt;\n\n      while (pt) {\n        _reverting ? pt.set(pt.t, pt.p, pt.b, pt) : pt.r(ratio, pt.d);\n        pt = pt._next;\n      }\n    }\n  }, {\n    name: \"endArray\",\n    headless: 1,\n    init: function init(target, value) {\n      var i = value.length;\n\n      while (i--) {\n        this.add(target, i, target[i] || 0, value[i], 0, 0, 0, 0, 0, 1);\n      }\n    }\n  }, _buildModifierPlugin(\"roundProps\", _roundModifier), _buildModifierPlugin(\"modifiers\"), _buildModifierPlugin(\"snap\", snap)) || _gsap;\n  Tween.version = Timeline.version = gsap.version = \"3.13.0\";\n  _coreReady = 1;\n  _windowExists() && _wake();\n  var Power0 = _easeMap.Power0,\n      Power1 = _easeMap.Power1,\n      Power2 = _easeMap.Power2,\n      Power3 = _easeMap.Power3,\n      Power4 = _easeMap.Power4,\n      Linear = _easeMap.Linear,\n      Quad = _easeMap.Quad,\n      Cubic = _easeMap.Cubic,\n      Quart = _easeMap.Quart,\n      Quint = _easeMap.Quint,\n      Strong = _easeMap.Strong,\n      Elastic = _easeMap.Elastic,\n      Back = _easeMap.Back,\n      SteppedEase = _easeMap.SteppedEase,\n      Bounce = _easeMap.Bounce,\n      Sine = _easeMap.Sine,\n      Expo = _easeMap.Expo,\n      Circ = _easeMap.Circ;\n\n  var _win$1,\n      _doc$1,\n      _docElement,\n      _pluginInitted,\n      _tempDiv,\n      _tempDivStyler,\n      _recentSetterPlugin,\n      _reverting$1,\n      _windowExists$1 = function _windowExists() {\n    return typeof window !== \"undefined\";\n  },\n      _transformProps = {},\n      _RAD2DEG = 180 / Math.PI,\n      _DEG2RAD = Math.PI / 180,\n      _atan2 = Math.atan2,\n      _bigNum$1 = 1e8,\n      _capsExp = /([A-Z])/g,\n      _horizontalExp = /(left|right|width|margin|padding|x)/i,\n      _complexExp = /[\\s,\\(]\\S/,\n      _propertyAliases = {\n    autoAlpha: \"opacity,visibility\",\n    scale: \"scaleX,scaleY\",\n    alpha: \"opacity\"\n  },\n      _renderCSSProp = function _renderCSSProp(ratio, data) {\n    return data.set(data.t, data.p, Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n  },\n      _renderPropWithEnd = function _renderPropWithEnd(ratio, data) {\n    return data.set(data.t, data.p, ratio === 1 ? data.e : Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n  },\n      _renderCSSPropWithBeginning = function _renderCSSPropWithBeginning(ratio, data) {\n    return data.set(data.t, data.p, ratio ? Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u : data.b, data);\n  },\n      _renderRoundedCSSProp = function _renderRoundedCSSProp(ratio, data) {\n    var value = data.s + data.c * ratio;\n    data.set(data.t, data.p, ~~(value + (value < 0 ? -.5 : .5)) + data.u, data);\n  },\n      _renderNonTweeningValue = function _renderNonTweeningValue(ratio, data) {\n    return data.set(data.t, data.p, ratio ? data.e : data.b, data);\n  },\n      _renderNonTweeningValueOnlyAtEnd = function _renderNonTweeningValueOnlyAtEnd(ratio, data) {\n    return data.set(data.t, data.p, ratio !== 1 ? data.b : data.e, data);\n  },\n      _setterCSSStyle = function _setterCSSStyle(target, property, value) {\n    return target.style[property] = value;\n  },\n      _setterCSSProp = function _setterCSSProp(target, property, value) {\n    return target.style.setProperty(property, value);\n  },\n      _setterTransform = function _setterTransform(target, property, value) {\n    return target._gsap[property] = value;\n  },\n      _setterScale = function _setterScale(target, property, value) {\n    return target._gsap.scaleX = target._gsap.scaleY = value;\n  },\n      _setterScaleWithRender = function _setterScaleWithRender(target, property, value, data, ratio) {\n    var cache = target._gsap;\n    cache.scaleX = cache.scaleY = value;\n    cache.renderTransform(ratio, cache);\n  },\n      _setterTransformWithRender = function _setterTransformWithRender(target, property, value, data, ratio) {\n    var cache = target._gsap;\n    cache[property] = value;\n    cache.renderTransform(ratio, cache);\n  },\n      _transformProp = \"transform\",\n      _transformOriginProp = _transformProp + \"Origin\",\n      _saveStyle = function _saveStyle(property, isNotCSS) {\n    var _this = this;\n\n    var target = this.target,\n        style = target.style,\n        cache = target._gsap;\n\n    if (property in _transformProps && style) {\n      this.tfm = this.tfm || {};\n\n      if (property !== \"transform\") {\n        property = _propertyAliases[property] || property;\n        ~property.indexOf(\",\") ? property.split(\",\").forEach(function (a) {\n          return _this.tfm[a] = _get(target, a);\n        }) : this.tfm[property] = cache.x ? cache[property] : _get(target, property);\n        property === _transformOriginProp && (this.tfm.zOrigin = cache.zOrigin);\n      } else {\n        return _propertyAliases.transform.split(\",\").forEach(function (p) {\n          return _saveStyle.call(_this, p, isNotCSS);\n        });\n      }\n\n      if (this.props.indexOf(_transformProp) >= 0) {\n        return;\n      }\n\n      if (cache.svg) {\n        this.svgo = target.getAttribute(\"data-svg-origin\");\n        this.props.push(_transformOriginProp, isNotCSS, \"\");\n      }\n\n      property = _transformProp;\n    }\n\n    (style || isNotCSS) && this.props.push(property, isNotCSS, style[property]);\n  },\n      _removeIndependentTransforms = function _removeIndependentTransforms(style) {\n    if (style.translate) {\n      style.removeProperty(\"translate\");\n      style.removeProperty(\"scale\");\n      style.removeProperty(\"rotate\");\n    }\n  },\n      _revertStyle = function _revertStyle() {\n    var props = this.props,\n        target = this.target,\n        style = target.style,\n        cache = target._gsap,\n        i,\n        p;\n\n    for (i = 0; i < props.length; i += 3) {\n      if (!props[i + 1]) {\n        props[i + 2] ? style[props[i]] = props[i + 2] : style.removeProperty(props[i].substr(0, 2) === \"--\" ? props[i] : props[i].replace(_capsExp, \"-$1\").toLowerCase());\n      } else if (props[i + 1] === 2) {\n        target[props[i]](props[i + 2]);\n      } else {\n        target[props[i]] = props[i + 2];\n      }\n    }\n\n    if (this.tfm) {\n      for (p in this.tfm) {\n        cache[p] = this.tfm[p];\n      }\n\n      if (cache.svg) {\n        cache.renderTransform();\n        target.setAttribute(\"data-svg-origin\", this.svgo || \"\");\n      }\n\n      i = _reverting$1();\n\n      if ((!i || !i.isStart) && !style[_transformProp]) {\n        _removeIndependentTransforms(style);\n\n        if (cache.zOrigin && style[_transformOriginProp]) {\n          style[_transformOriginProp] += \" \" + cache.zOrigin + \"px\";\n          cache.zOrigin = 0;\n          cache.renderTransform();\n        }\n\n        cache.uncache = 1;\n      }\n    }\n  },\n      _getStyleSaver = function _getStyleSaver(target, properties) {\n    var saver = {\n      target: target,\n      props: [],\n      revert: _revertStyle,\n      save: _saveStyle\n    };\n    target._gsap || gsap.core.getCache(target);\n    properties && target.style && target.nodeType && properties.split(\",\").forEach(function (p) {\n      return saver.save(p);\n    });\n    return saver;\n  },\n      _supports3D,\n      _createElement = function _createElement(type, ns) {\n    var e = _doc$1.createElementNS ? _doc$1.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc$1.createElement(type);\n    return e && e.style ? e : _doc$1.createElement(type);\n  },\n      _getComputedProperty = function _getComputedProperty(target, property, skipPrefixFallback) {\n    var cs = getComputedStyle(target);\n    return cs[property] || cs.getPropertyValue(property.replace(_capsExp, \"-$1\").toLowerCase()) || cs.getPropertyValue(property) || !skipPrefixFallback && _getComputedProperty(target, _checkPropPrefix(property) || property, 1) || \"\";\n  },\n      _prefixes = \"O,Moz,ms,Ms,Webkit\".split(\",\"),\n      _checkPropPrefix = function _checkPropPrefix(property, element, preferPrefix) {\n    var e = element || _tempDiv,\n        s = e.style,\n        i = 5;\n\n    if (property in s && !preferPrefix) {\n      return property;\n    }\n\n    property = property.charAt(0).toUpperCase() + property.substr(1);\n\n    while (i-- && !(_prefixes[i] + property in s)) {}\n\n    return i < 0 ? null : (i === 3 ? \"ms\" : i >= 0 ? _prefixes[i] : \"\") + property;\n  },\n      _initCore = function _initCore() {\n    if (_windowExists$1() && window.document) {\n      _win$1 = window;\n      _doc$1 = _win$1.document;\n      _docElement = _doc$1.documentElement;\n      _tempDiv = _createElement(\"div\") || {\n        style: {}\n      };\n      _tempDivStyler = _createElement(\"div\");\n      _transformProp = _checkPropPrefix(_transformProp);\n      _transformOriginProp = _transformProp + \"Origin\";\n      _tempDiv.style.cssText = \"border-width:0;line-height:0;position:absolute;padding:0\";\n      _supports3D = !!_checkPropPrefix(\"perspective\");\n      _reverting$1 = gsap.core.reverting;\n      _pluginInitted = 1;\n    }\n  },\n      _getReparentedCloneBBox = function _getReparentedCloneBBox(target) {\n    var owner = target.ownerSVGElement,\n        svg = _createElement(\"svg\", owner && owner.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\"),\n        clone = target.cloneNode(true),\n        bbox;\n\n    clone.style.display = \"block\";\n    svg.appendChild(clone);\n\n    _docElement.appendChild(svg);\n\n    try {\n      bbox = clone.getBBox();\n    } catch (e) {}\n\n    svg.removeChild(clone);\n\n    _docElement.removeChild(svg);\n\n    return bbox;\n  },\n      _getAttributeFallbacks = function _getAttributeFallbacks(target, attributesArray) {\n    var i = attributesArray.length;\n\n    while (i--) {\n      if (target.hasAttribute(attributesArray[i])) {\n        return target.getAttribute(attributesArray[i]);\n      }\n    }\n  },\n      _getBBox = function _getBBox(target) {\n    var bounds, cloned;\n\n    try {\n      bounds = target.getBBox();\n    } catch (error) {\n      bounds = _getReparentedCloneBBox(target);\n      cloned = 1;\n    }\n\n    bounds && (bounds.width || bounds.height) || cloned || (bounds = _getReparentedCloneBBox(target));\n    return bounds && !bounds.width && !bounds.x && !bounds.y ? {\n      x: +_getAttributeFallbacks(target, [\"x\", \"cx\", \"x1\"]) || 0,\n      y: +_getAttributeFallbacks(target, [\"y\", \"cy\", \"y1\"]) || 0,\n      width: 0,\n      height: 0\n    } : bounds;\n  },\n      _isSVG = function _isSVG(e) {\n    return !!(e.getCTM && (!e.parentNode || e.ownerSVGElement) && _getBBox(e));\n  },\n      _removeProperty = function _removeProperty(target, property) {\n    if (property) {\n      var style = target.style,\n          first2Chars;\n\n      if (property in _transformProps && property !== _transformOriginProp) {\n        property = _transformProp;\n      }\n\n      if (style.removeProperty) {\n        first2Chars = property.substr(0, 2);\n\n        if (first2Chars === \"ms\" || property.substr(0, 6) === \"webkit\") {\n          property = \"-\" + property;\n        }\n\n        style.removeProperty(first2Chars === \"--\" ? property : property.replace(_capsExp, \"-$1\").toLowerCase());\n      } else {\n        style.removeAttribute(property);\n      }\n    }\n  },\n      _addNonTweeningPT = function _addNonTweeningPT(plugin, target, property, beginning, end, onlySetAtEnd) {\n    var pt = new PropTween(plugin._pt, target, property, 0, 1, onlySetAtEnd ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue);\n    plugin._pt = pt;\n    pt.b = beginning;\n    pt.e = end;\n\n    plugin._props.push(property);\n\n    return pt;\n  },\n      _nonConvertibleUnits = {\n    deg: 1,\n    rad: 1,\n    turn: 1\n  },\n      _nonStandardLayouts = {\n    grid: 1,\n    flex: 1\n  },\n      _convertToUnit = function _convertToUnit(target, property, value, unit) {\n    var curValue = parseFloat(value) || 0,\n        curUnit = (value + \"\").trim().substr((curValue + \"\").length) || \"px\",\n        style = _tempDiv.style,\n        horizontal = _horizontalExp.test(property),\n        isRootSVG = target.tagName.toLowerCase() === \"svg\",\n        measureProperty = (isRootSVG ? \"client\" : \"offset\") + (horizontal ? \"Width\" : \"Height\"),\n        amount = 100,\n        toPixels = unit === \"px\",\n        toPercent = unit === \"%\",\n        px,\n        parent,\n        cache,\n        isSVG;\n\n    if (unit === curUnit || !curValue || _nonConvertibleUnits[unit] || _nonConvertibleUnits[curUnit]) {\n      return curValue;\n    }\n\n    curUnit !== \"px\" && !toPixels && (curValue = _convertToUnit(target, property, value, \"px\"));\n    isSVG = target.getCTM && _isSVG(target);\n\n    if ((toPercent || curUnit === \"%\") && (_transformProps[property] || ~property.indexOf(\"adius\"))) {\n      px = isSVG ? target.getBBox()[horizontal ? \"width\" : \"height\"] : target[measureProperty];\n      return _round(toPercent ? curValue / px * amount : curValue / 100 * px);\n    }\n\n    style[horizontal ? \"width\" : \"height\"] = amount + (toPixels ? curUnit : unit);\n    parent = unit !== \"rem\" && ~property.indexOf(\"adius\") || unit === \"em\" && target.appendChild && !isRootSVG ? target : target.parentNode;\n\n    if (isSVG) {\n      parent = (target.ownerSVGElement || {}).parentNode;\n    }\n\n    if (!parent || parent === _doc$1 || !parent.appendChild) {\n      parent = _doc$1.body;\n    }\n\n    cache = parent._gsap;\n\n    if (cache && toPercent && cache.width && horizontal && cache.time === _ticker.time && !cache.uncache) {\n      return _round(curValue / cache.width * amount);\n    } else {\n      if (toPercent && (property === \"height\" || property === \"width\")) {\n        var v = target.style[property];\n        target.style[property] = amount + unit;\n        px = target[measureProperty];\n        v ? target.style[property] = v : _removeProperty(target, property);\n      } else {\n        (toPercent || curUnit === \"%\") && !_nonStandardLayouts[_getComputedProperty(parent, \"display\")] && (style.position = _getComputedProperty(target, \"position\"));\n        parent === target && (style.position = \"static\");\n        parent.appendChild(_tempDiv);\n        px = _tempDiv[measureProperty];\n        parent.removeChild(_tempDiv);\n        style.position = \"absolute\";\n      }\n\n      if (horizontal && toPercent) {\n        cache = _getCache(parent);\n        cache.time = _ticker.time;\n        cache.width = parent[measureProperty];\n      }\n    }\n\n    return _round(toPixels ? px * curValue / amount : px && curValue ? amount / px * curValue : 0);\n  },\n      _get = function _get(target, property, unit, uncache) {\n    var value;\n    _pluginInitted || _initCore();\n\n    if (property in _propertyAliases && property !== \"transform\") {\n      property = _propertyAliases[property];\n\n      if (~property.indexOf(\",\")) {\n        property = property.split(\",\")[0];\n      }\n    }\n\n    if (_transformProps[property] && property !== \"transform\") {\n      value = _parseTransform(target, uncache);\n      value = property !== \"transformOrigin\" ? value[property] : value.svg ? value.origin : _firstTwoOnly(_getComputedProperty(target, _transformOriginProp)) + \" \" + value.zOrigin + \"px\";\n    } else {\n      value = target.style[property];\n\n      if (!value || value === \"auto\" || uncache || ~(value + \"\").indexOf(\"calc(\")) {\n        value = _specialProps[property] && _specialProps[property](target, property, unit) || _getComputedProperty(target, property) || _getProperty(target, property) || (property === \"opacity\" ? 1 : 0);\n      }\n    }\n\n    return unit && !~(value + \"\").trim().indexOf(\" \") ? _convertToUnit(target, property, value, unit) + unit : value;\n  },\n      _tweenComplexCSSString = function _tweenComplexCSSString(target, prop, start, end) {\n    if (!start || start === \"none\") {\n      var p = _checkPropPrefix(prop, target, 1),\n          s = p && _getComputedProperty(target, p, 1);\n\n      if (s && s !== start) {\n        prop = p;\n        start = s;\n      } else if (prop === \"borderColor\") {\n        start = _getComputedProperty(target, \"borderTopColor\");\n      }\n    }\n\n    var pt = new PropTween(this._pt, target.style, prop, 0, 1, _renderComplexString),\n        index = 0,\n        matchIndex = 0,\n        a,\n        result,\n        startValues,\n        startNum,\n        color,\n        startValue,\n        endValue,\n        endNum,\n        chunk,\n        endUnit,\n        startUnit,\n        endValues;\n    pt.b = start;\n    pt.e = end;\n    start += \"\";\n    end += \"\";\n\n    if (end.substring(0, 6) === \"var(--\") {\n      end = _getComputedProperty(target, end.substring(4, end.indexOf(\")\")));\n    }\n\n    if (end === \"auto\") {\n      startValue = target.style[prop];\n      target.style[prop] = end;\n      end = _getComputedProperty(target, prop) || end;\n      startValue ? target.style[prop] = startValue : _removeProperty(target, prop);\n    }\n\n    a = [start, end];\n\n    _colorStringFilter(a);\n\n    start = a[0];\n    end = a[1];\n    startValues = start.match(_numWithUnitExp) || [];\n    endValues = end.match(_numWithUnitExp) || [];\n\n    if (endValues.length) {\n      while (result = _numWithUnitExp.exec(end)) {\n        endValue = result[0];\n        chunk = end.substring(index, result.index);\n\n        if (color) {\n          color = (color + 1) % 5;\n        } else if (chunk.substr(-5) === \"rgba(\" || chunk.substr(-5) === \"hsla(\") {\n          color = 1;\n        }\n\n        if (endValue !== (startValue = startValues[matchIndex++] || \"\")) {\n          startNum = parseFloat(startValue) || 0;\n          startUnit = startValue.substr((startNum + \"\").length);\n          endValue.charAt(1) === \"=\" && (endValue = _parseRelative(startNum, endValue) + startUnit);\n          endNum = parseFloat(endValue);\n          endUnit = endValue.substr((endNum + \"\").length);\n          index = _numWithUnitExp.lastIndex - endUnit.length;\n\n          if (!endUnit) {\n            endUnit = endUnit || _config.units[prop] || startUnit;\n\n            if (index === end.length) {\n              end += endUnit;\n              pt.e += endUnit;\n            }\n          }\n\n          if (startUnit !== endUnit) {\n            startNum = _convertToUnit(target, prop, startValue, endUnit) || 0;\n          }\n\n          pt._pt = {\n            _next: pt._pt,\n            p: chunk || matchIndex === 1 ? chunk : \",\",\n            s: startNum,\n            c: endNum - startNum,\n            m: color && color < 4 || prop === \"zIndex\" ? Math.round : 0\n          };\n        }\n      }\n\n      pt.c = index < end.length ? end.substring(index, end.length) : \"\";\n    } else {\n      pt.r = prop === \"display\" && end === \"none\" ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue;\n    }\n\n    _relExp.test(end) && (pt.e = 0);\n    this._pt = pt;\n    return pt;\n  },\n      _keywordToPercent = {\n    top: \"0%\",\n    bottom: \"100%\",\n    left: \"0%\",\n    right: \"100%\",\n    center: \"50%\"\n  },\n      _convertKeywordsToPercentages = function _convertKeywordsToPercentages(value) {\n    var split = value.split(\" \"),\n        x = split[0],\n        y = split[1] || \"50%\";\n\n    if (x === \"top\" || x === \"bottom\" || y === \"left\" || y === \"right\") {\n      value = x;\n      x = y;\n      y = value;\n    }\n\n    split[0] = _keywordToPercent[x] || x;\n    split[1] = _keywordToPercent[y] || y;\n    return split.join(\" \");\n  },\n      _renderClearProps = function _renderClearProps(ratio, data) {\n    if (data.tween && data.tween._time === data.tween._dur) {\n      var target = data.t,\n          style = target.style,\n          props = data.u,\n          cache = target._gsap,\n          prop,\n          clearTransforms,\n          i;\n\n      if (props === \"all\" || props === true) {\n        style.cssText = \"\";\n        clearTransforms = 1;\n      } else {\n        props = props.split(\",\");\n        i = props.length;\n\n        while (--i > -1) {\n          prop = props[i];\n\n          if (_transformProps[prop]) {\n            clearTransforms = 1;\n            prop = prop === \"transformOrigin\" ? _transformOriginProp : _transformProp;\n          }\n\n          _removeProperty(target, prop);\n        }\n      }\n\n      if (clearTransforms) {\n        _removeProperty(target, _transformProp);\n\n        if (cache) {\n          cache.svg && target.removeAttribute(\"transform\");\n          style.scale = style.rotate = style.translate = \"none\";\n\n          _parseTransform(target, 1);\n\n          cache.uncache = 1;\n\n          _removeIndependentTransforms(style);\n        }\n      }\n    }\n  },\n      _specialProps = {\n    clearProps: function clearProps(plugin, target, property, endValue, tween) {\n      if (tween.data !== \"isFromStart\") {\n        var pt = plugin._pt = new PropTween(plugin._pt, target, property, 0, 0, _renderClearProps);\n        pt.u = endValue;\n        pt.pr = -10;\n        pt.tween = tween;\n\n        plugin._props.push(property);\n\n        return 1;\n      }\n    }\n  },\n      _identity2DMatrix = [1, 0, 0, 1, 0, 0],\n      _rotationalProperties = {},\n      _isNullTransform = function _isNullTransform(value) {\n    return value === \"matrix(1, 0, 0, 1, 0, 0)\" || value === \"none\" || !value;\n  },\n      _getComputedTransformMatrixAsArray = function _getComputedTransformMatrixAsArray(target) {\n    var matrixString = _getComputedProperty(target, _transformProp);\n\n    return _isNullTransform(matrixString) ? _identity2DMatrix : matrixString.substr(7).match(_numExp).map(_round);\n  },\n      _getMatrix = function _getMatrix(target, force2D) {\n    var cache = target._gsap || _getCache(target),\n        style = target.style,\n        matrix = _getComputedTransformMatrixAsArray(target),\n        parent,\n        nextSibling,\n        temp,\n        addedToDOM;\n\n    if (cache.svg && target.getAttribute(\"transform\")) {\n      temp = target.transform.baseVal.consolidate().matrix;\n      matrix = [temp.a, temp.b, temp.c, temp.d, temp.e, temp.f];\n      return matrix.join(\",\") === \"1,0,0,1,0,0\" ? _identity2DMatrix : matrix;\n    } else if (matrix === _identity2DMatrix && !target.offsetParent && target !== _docElement && !cache.svg) {\n      temp = style.display;\n      style.display = \"block\";\n      parent = target.parentNode;\n\n      if (!parent || !target.offsetParent && !target.getBoundingClientRect().width) {\n        addedToDOM = 1;\n        nextSibling = target.nextElementSibling;\n\n        _docElement.appendChild(target);\n      }\n\n      matrix = _getComputedTransformMatrixAsArray(target);\n      temp ? style.display = temp : _removeProperty(target, \"display\");\n\n      if (addedToDOM) {\n        nextSibling ? parent.insertBefore(target, nextSibling) : parent ? parent.appendChild(target) : _docElement.removeChild(target);\n      }\n    }\n\n    return force2D && matrix.length > 6 ? [matrix[0], matrix[1], matrix[4], matrix[5], matrix[12], matrix[13]] : matrix;\n  },\n      _applySVGOrigin = function _applySVGOrigin(target, origin, originIsAbsolute, smooth, matrixArray, pluginToAddPropTweensTo) {\n    var cache = target._gsap,\n        matrix = matrixArray || _getMatrix(target, true),\n        xOriginOld = cache.xOrigin || 0,\n        yOriginOld = cache.yOrigin || 0,\n        xOffsetOld = cache.xOffset || 0,\n        yOffsetOld = cache.yOffset || 0,\n        a = matrix[0],\n        b = matrix[1],\n        c = matrix[2],\n        d = matrix[3],\n        tx = matrix[4],\n        ty = matrix[5],\n        originSplit = origin.split(\" \"),\n        xOrigin = parseFloat(originSplit[0]) || 0,\n        yOrigin = parseFloat(originSplit[1]) || 0,\n        bounds,\n        determinant,\n        x,\n        y;\n\n    if (!originIsAbsolute) {\n      bounds = _getBBox(target);\n      xOrigin = bounds.x + (~originSplit[0].indexOf(\"%\") ? xOrigin / 100 * bounds.width : xOrigin);\n      yOrigin = bounds.y + (~(originSplit[1] || originSplit[0]).indexOf(\"%\") ? yOrigin / 100 * bounds.height : yOrigin);\n    } else if (matrix !== _identity2DMatrix && (determinant = a * d - b * c)) {\n      x = xOrigin * (d / determinant) + yOrigin * (-c / determinant) + (c * ty - d * tx) / determinant;\n      y = xOrigin * (-b / determinant) + yOrigin * (a / determinant) - (a * ty - b * tx) / determinant;\n      xOrigin = x;\n      yOrigin = y;\n    }\n\n    if (smooth || smooth !== false && cache.smooth) {\n      tx = xOrigin - xOriginOld;\n      ty = yOrigin - yOriginOld;\n      cache.xOffset = xOffsetOld + (tx * a + ty * c) - tx;\n      cache.yOffset = yOffsetOld + (tx * b + ty * d) - ty;\n    } else {\n      cache.xOffset = cache.yOffset = 0;\n    }\n\n    cache.xOrigin = xOrigin;\n    cache.yOrigin = yOrigin;\n    cache.smooth = !!smooth;\n    cache.origin = origin;\n    cache.originIsAbsolute = !!originIsAbsolute;\n    target.style[_transformOriginProp] = \"0px 0px\";\n\n    if (pluginToAddPropTweensTo) {\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOrigin\", xOriginOld, xOrigin);\n\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOrigin\", yOriginOld, yOrigin);\n\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOffset\", xOffsetOld, cache.xOffset);\n\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOffset\", yOffsetOld, cache.yOffset);\n    }\n\n    target.setAttribute(\"data-svg-origin\", xOrigin + \" \" + yOrigin);\n  },\n      _parseTransform = function _parseTransform(target, uncache) {\n    var cache = target._gsap || new GSCache(target);\n\n    if (\"x\" in cache && !uncache && !cache.uncache) {\n      return cache;\n    }\n\n    var style = target.style,\n        invertedScaleX = cache.scaleX < 0,\n        px = \"px\",\n        deg = \"deg\",\n        cs = getComputedStyle(target),\n        origin = _getComputedProperty(target, _transformOriginProp) || \"0\",\n        x,\n        y,\n        z,\n        scaleX,\n        scaleY,\n        rotation,\n        rotationX,\n        rotationY,\n        skewX,\n        skewY,\n        perspective,\n        xOrigin,\n        yOrigin,\n        matrix,\n        angle,\n        cos,\n        sin,\n        a,\n        b,\n        c,\n        d,\n        a12,\n        a22,\n        t1,\n        t2,\n        t3,\n        a13,\n        a23,\n        a33,\n        a42,\n        a43,\n        a32;\n    x = y = z = rotation = rotationX = rotationY = skewX = skewY = perspective = 0;\n    scaleX = scaleY = 1;\n    cache.svg = !!(target.getCTM && _isSVG(target));\n\n    if (cs.translate) {\n      if (cs.translate !== \"none\" || cs.scale !== \"none\" || cs.rotate !== \"none\") {\n        style[_transformProp] = (cs.translate !== \"none\" ? \"translate3d(\" + (cs.translate + \" 0 0\").split(\" \").slice(0, 3).join(\", \") + \") \" : \"\") + (cs.rotate !== \"none\" ? \"rotate(\" + cs.rotate + \") \" : \"\") + (cs.scale !== \"none\" ? \"scale(\" + cs.scale.split(\" \").join(\",\") + \") \" : \"\") + (cs[_transformProp] !== \"none\" ? cs[_transformProp] : \"\");\n      }\n\n      style.scale = style.rotate = style.translate = \"none\";\n    }\n\n    matrix = _getMatrix(target, cache.svg);\n\n    if (cache.svg) {\n      if (cache.uncache) {\n        t2 = target.getBBox();\n        origin = cache.xOrigin - t2.x + \"px \" + (cache.yOrigin - t2.y) + \"px\";\n        t1 = \"\";\n      } else {\n        t1 = !uncache && target.getAttribute(\"data-svg-origin\");\n      }\n\n      _applySVGOrigin(target, t1 || origin, !!t1 || cache.originIsAbsolute, cache.smooth !== false, matrix);\n    }\n\n    xOrigin = cache.xOrigin || 0;\n    yOrigin = cache.yOrigin || 0;\n\n    if (matrix !== _identity2DMatrix) {\n      a = matrix[0];\n      b = matrix[1];\n      c = matrix[2];\n      d = matrix[3];\n      x = a12 = matrix[4];\n      y = a22 = matrix[5];\n\n      if (matrix.length === 6) {\n        scaleX = Math.sqrt(a * a + b * b);\n        scaleY = Math.sqrt(d * d + c * c);\n        rotation = a || b ? _atan2(b, a) * _RAD2DEG : 0;\n        skewX = c || d ? _atan2(c, d) * _RAD2DEG + rotation : 0;\n        skewX && (scaleY *= Math.abs(Math.cos(skewX * _DEG2RAD)));\n\n        if (cache.svg) {\n          x -= xOrigin - (xOrigin * a + yOrigin * c);\n          y -= yOrigin - (xOrigin * b + yOrigin * d);\n        }\n      } else {\n        a32 = matrix[6];\n        a42 = matrix[7];\n        a13 = matrix[8];\n        a23 = matrix[9];\n        a33 = matrix[10];\n        a43 = matrix[11];\n        x = matrix[12];\n        y = matrix[13];\n        z = matrix[14];\n        angle = _atan2(a32, a33);\n        rotationX = angle * _RAD2DEG;\n\n        if (angle) {\n          cos = Math.cos(-angle);\n          sin = Math.sin(-angle);\n          t1 = a12 * cos + a13 * sin;\n          t2 = a22 * cos + a23 * sin;\n          t3 = a32 * cos + a33 * sin;\n          a13 = a12 * -sin + a13 * cos;\n          a23 = a22 * -sin + a23 * cos;\n          a33 = a32 * -sin + a33 * cos;\n          a43 = a42 * -sin + a43 * cos;\n          a12 = t1;\n          a22 = t2;\n          a32 = t3;\n        }\n\n        angle = _atan2(-c, a33);\n        rotationY = angle * _RAD2DEG;\n\n        if (angle) {\n          cos = Math.cos(-angle);\n          sin = Math.sin(-angle);\n          t1 = a * cos - a13 * sin;\n          t2 = b * cos - a23 * sin;\n          t3 = c * cos - a33 * sin;\n          a43 = d * sin + a43 * cos;\n          a = t1;\n          b = t2;\n          c = t3;\n        }\n\n        angle = _atan2(b, a);\n        rotation = angle * _RAD2DEG;\n\n        if (angle) {\n          cos = Math.cos(angle);\n          sin = Math.sin(angle);\n          t1 = a * cos + b * sin;\n          t2 = a12 * cos + a22 * sin;\n          b = b * cos - a * sin;\n          a22 = a22 * cos - a12 * sin;\n          a = t1;\n          a12 = t2;\n        }\n\n        if (rotationX && Math.abs(rotationX) + Math.abs(rotation) > 359.9) {\n          rotationX = rotation = 0;\n          rotationY = 180 - rotationY;\n        }\n\n        scaleX = _round(Math.sqrt(a * a + b * b + c * c));\n        scaleY = _round(Math.sqrt(a22 * a22 + a32 * a32));\n        angle = _atan2(a12, a22);\n        skewX = Math.abs(angle) > 0.0002 ? angle * _RAD2DEG : 0;\n        perspective = a43 ? 1 / (a43 < 0 ? -a43 : a43) : 0;\n      }\n\n      if (cache.svg) {\n        t1 = target.getAttribute(\"transform\");\n        cache.forceCSS = target.setAttribute(\"transform\", \"\") || !_isNullTransform(_getComputedProperty(target, _transformProp));\n        t1 && target.setAttribute(\"transform\", t1);\n      }\n    }\n\n    if (Math.abs(skewX) > 90 && Math.abs(skewX) < 270) {\n      if (invertedScaleX) {\n        scaleX *= -1;\n        skewX += rotation <= 0 ? 180 : -180;\n        rotation += rotation <= 0 ? 180 : -180;\n      } else {\n        scaleY *= -1;\n        skewX += skewX <= 0 ? 180 : -180;\n      }\n    }\n\n    uncache = uncache || cache.uncache;\n    cache.x = x - ((cache.xPercent = x && (!uncache && cache.xPercent || (Math.round(target.offsetWidth / 2) === Math.round(-x) ? -50 : 0))) ? target.offsetWidth * cache.xPercent / 100 : 0) + px;\n    cache.y = y - ((cache.yPercent = y && (!uncache && cache.yPercent || (Math.round(target.offsetHeight / 2) === Math.round(-y) ? -50 : 0))) ? target.offsetHeight * cache.yPercent / 100 : 0) + px;\n    cache.z = z + px;\n    cache.scaleX = _round(scaleX);\n    cache.scaleY = _round(scaleY);\n    cache.rotation = _round(rotation) + deg;\n    cache.rotationX = _round(rotationX) + deg;\n    cache.rotationY = _round(rotationY) + deg;\n    cache.skewX = skewX + deg;\n    cache.skewY = skewY + deg;\n    cache.transformPerspective = perspective + px;\n\n    if (cache.zOrigin = parseFloat(origin.split(\" \")[2]) || !uncache && cache.zOrigin || 0) {\n      style[_transformOriginProp] = _firstTwoOnly(origin);\n    }\n\n    cache.xOffset = cache.yOffset = 0;\n    cache.force3D = _config.force3D;\n    cache.renderTransform = cache.svg ? _renderSVGTransforms : _supports3D ? _renderCSSTransforms : _renderNon3DTransforms;\n    cache.uncache = 0;\n    return cache;\n  },\n      _firstTwoOnly = function _firstTwoOnly(value) {\n    return (value = value.split(\" \"))[0] + \" \" + value[1];\n  },\n      _addPxTranslate = function _addPxTranslate(target, start, value) {\n    var unit = getUnit(start);\n    return _round(parseFloat(start) + parseFloat(_convertToUnit(target, \"x\", value + \"px\", unit))) + unit;\n  },\n      _renderNon3DTransforms = function _renderNon3DTransforms(ratio, cache) {\n    cache.z = \"0px\";\n    cache.rotationY = cache.rotationX = \"0deg\";\n    cache.force3D = 0;\n\n    _renderCSSTransforms(ratio, cache);\n  },\n      _zeroDeg = \"0deg\",\n      _zeroPx = \"0px\",\n      _endParenthesis = \") \",\n      _renderCSSTransforms = function _renderCSSTransforms(ratio, cache) {\n    var _ref = cache || this,\n        xPercent = _ref.xPercent,\n        yPercent = _ref.yPercent,\n        x = _ref.x,\n        y = _ref.y,\n        z = _ref.z,\n        rotation = _ref.rotation,\n        rotationY = _ref.rotationY,\n        rotationX = _ref.rotationX,\n        skewX = _ref.skewX,\n        skewY = _ref.skewY,\n        scaleX = _ref.scaleX,\n        scaleY = _ref.scaleY,\n        transformPerspective = _ref.transformPerspective,\n        force3D = _ref.force3D,\n        target = _ref.target,\n        zOrigin = _ref.zOrigin,\n        transforms = \"\",\n        use3D = force3D === \"auto\" && ratio && ratio !== 1 || force3D === true;\n\n    if (zOrigin && (rotationX !== _zeroDeg || rotationY !== _zeroDeg)) {\n      var angle = parseFloat(rotationY) * _DEG2RAD,\n          a13 = Math.sin(angle),\n          a33 = Math.cos(angle),\n          cos;\n\n      angle = parseFloat(rotationX) * _DEG2RAD;\n      cos = Math.cos(angle);\n      x = _addPxTranslate(target, x, a13 * cos * -zOrigin);\n      y = _addPxTranslate(target, y, -Math.sin(angle) * -zOrigin);\n      z = _addPxTranslate(target, z, a33 * cos * -zOrigin + zOrigin);\n    }\n\n    if (transformPerspective !== _zeroPx) {\n      transforms += \"perspective(\" + transformPerspective + _endParenthesis;\n    }\n\n    if (xPercent || yPercent) {\n      transforms += \"translate(\" + xPercent + \"%, \" + yPercent + \"%) \";\n    }\n\n    if (use3D || x !== _zeroPx || y !== _zeroPx || z !== _zeroPx) {\n      transforms += z !== _zeroPx || use3D ? \"translate3d(\" + x + \", \" + y + \", \" + z + \") \" : \"translate(\" + x + \", \" + y + _endParenthesis;\n    }\n\n    if (rotation !== _zeroDeg) {\n      transforms += \"rotate(\" + rotation + _endParenthesis;\n    }\n\n    if (rotationY !== _zeroDeg) {\n      transforms += \"rotateY(\" + rotationY + _endParenthesis;\n    }\n\n    if (rotationX !== _zeroDeg) {\n      transforms += \"rotateX(\" + rotationX + _endParenthesis;\n    }\n\n    if (skewX !== _zeroDeg || skewY !== _zeroDeg) {\n      transforms += \"skew(\" + skewX + \", \" + skewY + _endParenthesis;\n    }\n\n    if (scaleX !== 1 || scaleY !== 1) {\n      transforms += \"scale(\" + scaleX + \", \" + scaleY + _endParenthesis;\n    }\n\n    target.style[_transformProp] = transforms || \"translate(0, 0)\";\n  },\n      _renderSVGTransforms = function _renderSVGTransforms(ratio, cache) {\n    var _ref2 = cache || this,\n        xPercent = _ref2.xPercent,\n        yPercent = _ref2.yPercent,\n        x = _ref2.x,\n        y = _ref2.y,\n        rotation = _ref2.rotation,\n        skewX = _ref2.skewX,\n        skewY = _ref2.skewY,\n        scaleX = _ref2.scaleX,\n        scaleY = _ref2.scaleY,\n        target = _ref2.target,\n        xOrigin = _ref2.xOrigin,\n        yOrigin = _ref2.yOrigin,\n        xOffset = _ref2.xOffset,\n        yOffset = _ref2.yOffset,\n        forceCSS = _ref2.forceCSS,\n        tx = parseFloat(x),\n        ty = parseFloat(y),\n        a11,\n        a21,\n        a12,\n        a22,\n        temp;\n\n    rotation = parseFloat(rotation);\n    skewX = parseFloat(skewX);\n    skewY = parseFloat(skewY);\n\n    if (skewY) {\n      skewY = parseFloat(skewY);\n      skewX += skewY;\n      rotation += skewY;\n    }\n\n    if (rotation || skewX) {\n      rotation *= _DEG2RAD;\n      skewX *= _DEG2RAD;\n      a11 = Math.cos(rotation) * scaleX;\n      a21 = Math.sin(rotation) * scaleX;\n      a12 = Math.sin(rotation - skewX) * -scaleY;\n      a22 = Math.cos(rotation - skewX) * scaleY;\n\n      if (skewX) {\n        skewY *= _DEG2RAD;\n        temp = Math.tan(skewX - skewY);\n        temp = Math.sqrt(1 + temp * temp);\n        a12 *= temp;\n        a22 *= temp;\n\n        if (skewY) {\n          temp = Math.tan(skewY);\n          temp = Math.sqrt(1 + temp * temp);\n          a11 *= temp;\n          a21 *= temp;\n        }\n      }\n\n      a11 = _round(a11);\n      a21 = _round(a21);\n      a12 = _round(a12);\n      a22 = _round(a22);\n    } else {\n      a11 = scaleX;\n      a22 = scaleY;\n      a21 = a12 = 0;\n    }\n\n    if (tx && !~(x + \"\").indexOf(\"px\") || ty && !~(y + \"\").indexOf(\"px\")) {\n      tx = _convertToUnit(target, \"x\", x, \"px\");\n      ty = _convertToUnit(target, \"y\", y, \"px\");\n    }\n\n    if (xOrigin || yOrigin || xOffset || yOffset) {\n      tx = _round(tx + xOrigin - (xOrigin * a11 + yOrigin * a12) + xOffset);\n      ty = _round(ty + yOrigin - (xOrigin * a21 + yOrigin * a22) + yOffset);\n    }\n\n    if (xPercent || yPercent) {\n      temp = target.getBBox();\n      tx = _round(tx + xPercent / 100 * temp.width);\n      ty = _round(ty + yPercent / 100 * temp.height);\n    }\n\n    temp = \"matrix(\" + a11 + \",\" + a21 + \",\" + a12 + \",\" + a22 + \",\" + tx + \",\" + ty + \")\";\n    target.setAttribute(\"transform\", temp);\n    forceCSS && (target.style[_transformProp] = temp);\n  },\n      _addRotationalPropTween = function _addRotationalPropTween(plugin, target, property, startNum, endValue) {\n    var cap = 360,\n        isString = _isString(endValue),\n        endNum = parseFloat(endValue) * (isString && ~endValue.indexOf(\"rad\") ? _RAD2DEG : 1),\n        change = endNum - startNum,\n        finalValue = startNum + change + \"deg\",\n        direction,\n        pt;\n\n    if (isString) {\n      direction = endValue.split(\"_\")[1];\n\n      if (direction === \"short\") {\n        change %= cap;\n\n        if (change !== change % (cap / 2)) {\n          change += change < 0 ? cap : -cap;\n        }\n      }\n\n      if (direction === \"cw\" && change < 0) {\n        change = (change + cap * _bigNum$1) % cap - ~~(change / cap) * cap;\n      } else if (direction === \"ccw\" && change > 0) {\n        change = (change - cap * _bigNum$1) % cap - ~~(change / cap) * cap;\n      }\n    }\n\n    plugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n    pt.e = finalValue;\n    pt.u = \"deg\";\n\n    plugin._props.push(property);\n\n    return pt;\n  },\n      _assign = function _assign(target, source) {\n    for (var p in source) {\n      target[p] = source[p];\n    }\n\n    return target;\n  },\n      _addRawTransformPTs = function _addRawTransformPTs(plugin, transforms, target) {\n    var startCache = _assign({}, target._gsap),\n        exclude = \"perspective,force3D,transformOrigin,svgOrigin\",\n        style = target.style,\n        endCache,\n        p,\n        startValue,\n        endValue,\n        startNum,\n        endNum,\n        startUnit,\n        endUnit;\n\n    if (startCache.svg) {\n      startValue = target.getAttribute(\"transform\");\n      target.setAttribute(\"transform\", \"\");\n      style[_transformProp] = transforms;\n      endCache = _parseTransform(target, 1);\n\n      _removeProperty(target, _transformProp);\n\n      target.setAttribute(\"transform\", startValue);\n    } else {\n      startValue = getComputedStyle(target)[_transformProp];\n      style[_transformProp] = transforms;\n      endCache = _parseTransform(target, 1);\n      style[_transformProp] = startValue;\n    }\n\n    for (p in _transformProps) {\n      startValue = startCache[p];\n      endValue = endCache[p];\n\n      if (startValue !== endValue && exclude.indexOf(p) < 0) {\n        startUnit = getUnit(startValue);\n        endUnit = getUnit(endValue);\n        startNum = startUnit !== endUnit ? _convertToUnit(target, p, startValue, endUnit) : parseFloat(startValue);\n        endNum = parseFloat(endValue);\n        plugin._pt = new PropTween(plugin._pt, endCache, p, startNum, endNum - startNum, _renderCSSProp);\n        plugin._pt.u = endUnit || 0;\n\n        plugin._props.push(p);\n      }\n    }\n\n    _assign(endCache, startCache);\n  };\n\n  _forEachName(\"padding,margin,Width,Radius\", function (name, index) {\n    var t = \"Top\",\n        r = \"Right\",\n        b = \"Bottom\",\n        l = \"Left\",\n        props = (index < 3 ? [t, r, b, l] : [t + l, t + r, b + r, b + l]).map(function (side) {\n      return index < 2 ? name + side : \"border\" + side + name;\n    });\n\n    _specialProps[index > 1 ? \"border\" + name : name] = function (plugin, target, property, endValue, tween) {\n      var a, vars;\n\n      if (arguments.length < 4) {\n        a = props.map(function (prop) {\n          return _get(plugin, prop, property);\n        });\n        vars = a.join(\" \");\n        return vars.split(a[0]).length === 5 ? a[0] : vars;\n      }\n\n      a = (endValue + \"\").split(\" \");\n      vars = {};\n      props.forEach(function (prop, i) {\n        return vars[prop] = a[i] = a[i] || a[(i - 1) / 2 | 0];\n      });\n      plugin.init(target, vars, tween);\n    };\n  });\n\n  var CSSPlugin = {\n    name: \"css\",\n    register: _initCore,\n    targetTest: function targetTest(target) {\n      return target.style && target.nodeType;\n    },\n    init: function init(target, vars, tween, index, targets) {\n      var props = this._props,\n          style = target.style,\n          startAt = tween.vars.startAt,\n          startValue,\n          endValue,\n          endNum,\n          startNum,\n          type,\n          specialProp,\n          p,\n          startUnit,\n          endUnit,\n          relative,\n          isTransformRelated,\n          transformPropTween,\n          cache,\n          smooth,\n          hasPriority,\n          inlineProps;\n      _pluginInitted || _initCore();\n      this.styles = this.styles || _getStyleSaver(target);\n      inlineProps = this.styles.props;\n      this.tween = tween;\n\n      for (p in vars) {\n        if (p === \"autoRound\") {\n          continue;\n        }\n\n        endValue = vars[p];\n\n        if (_plugins[p] && _checkPlugin(p, vars, tween, index, target, targets)) {\n          continue;\n        }\n\n        type = typeof endValue;\n        specialProp = _specialProps[p];\n\n        if (type === \"function\") {\n          endValue = endValue.call(tween, index, target, targets);\n          type = typeof endValue;\n        }\n\n        if (type === \"string\" && ~endValue.indexOf(\"random(\")) {\n          endValue = _replaceRandom(endValue);\n        }\n\n        if (specialProp) {\n          specialProp(this, target, p, endValue, tween) && (hasPriority = 1);\n        } else if (p.substr(0, 2) === \"--\") {\n          startValue = (getComputedStyle(target).getPropertyValue(p) + \"\").trim();\n          endValue += \"\";\n          _colorExp.lastIndex = 0;\n\n          if (!_colorExp.test(startValue)) {\n            startUnit = getUnit(startValue);\n            endUnit = getUnit(endValue);\n          }\n\n          endUnit ? startUnit !== endUnit && (startValue = _convertToUnit(target, p, startValue, endUnit) + endUnit) : startUnit && (endValue += startUnit);\n          this.add(style, \"setProperty\", startValue, endValue, index, targets, 0, 0, p);\n          props.push(p);\n          inlineProps.push(p, 0, style[p]);\n        } else if (type !== \"undefined\") {\n          if (startAt && p in startAt) {\n            startValue = typeof startAt[p] === \"function\" ? startAt[p].call(tween, index, target, targets) : startAt[p];\n            _isString(startValue) && ~startValue.indexOf(\"random(\") && (startValue = _replaceRandom(startValue));\n            getUnit(startValue + \"\") || startValue === \"auto\" || (startValue += _config.units[p] || getUnit(_get(target, p)) || \"\");\n            (startValue + \"\").charAt(1) === \"=\" && (startValue = _get(target, p));\n          } else {\n            startValue = _get(target, p);\n          }\n\n          startNum = parseFloat(startValue);\n          relative = type === \"string\" && endValue.charAt(1) === \"=\" && endValue.substr(0, 2);\n          relative && (endValue = endValue.substr(2));\n          endNum = parseFloat(endValue);\n\n          if (p in _propertyAliases) {\n            if (p === \"autoAlpha\") {\n              if (startNum === 1 && _get(target, \"visibility\") === \"hidden\" && endNum) {\n                startNum = 0;\n              }\n\n              inlineProps.push(\"visibility\", 0, style.visibility);\n\n              _addNonTweeningPT(this, style, \"visibility\", startNum ? \"inherit\" : \"hidden\", endNum ? \"inherit\" : \"hidden\", !endNum);\n            }\n\n            if (p !== \"scale\" && p !== \"transform\") {\n              p = _propertyAliases[p];\n              ~p.indexOf(\",\") && (p = p.split(\",\")[0]);\n            }\n          }\n\n          isTransformRelated = p in _transformProps;\n\n          if (isTransformRelated) {\n            this.styles.save(p);\n\n            if (type === \"string\" && endValue.substring(0, 6) === \"var(--\") {\n              endValue = _getComputedProperty(target, endValue.substring(4, endValue.indexOf(\")\")));\n              endNum = parseFloat(endValue);\n            }\n\n            if (!transformPropTween) {\n              cache = target._gsap;\n              cache.renderTransform && !vars.parseTransform || _parseTransform(target, vars.parseTransform);\n              smooth = vars.smoothOrigin !== false && cache.smooth;\n              transformPropTween = this._pt = new PropTween(this._pt, style, _transformProp, 0, 1, cache.renderTransform, cache, 0, -1);\n              transformPropTween.dep = 1;\n            }\n\n            if (p === \"scale\") {\n              this._pt = new PropTween(this._pt, cache, \"scaleY\", cache.scaleY, (relative ? _parseRelative(cache.scaleY, relative + endNum) : endNum) - cache.scaleY || 0, _renderCSSProp);\n              this._pt.u = 0;\n              props.push(\"scaleY\", p);\n              p += \"X\";\n            } else if (p === \"transformOrigin\") {\n              inlineProps.push(_transformOriginProp, 0, style[_transformOriginProp]);\n              endValue = _convertKeywordsToPercentages(endValue);\n\n              if (cache.svg) {\n                _applySVGOrigin(target, endValue, 0, smooth, 0, this);\n              } else {\n                endUnit = parseFloat(endValue.split(\" \")[2]) || 0;\n                endUnit !== cache.zOrigin && _addNonTweeningPT(this, cache, \"zOrigin\", cache.zOrigin, endUnit);\n\n                _addNonTweeningPT(this, style, p, _firstTwoOnly(startValue), _firstTwoOnly(endValue));\n              }\n\n              continue;\n            } else if (p === \"svgOrigin\") {\n              _applySVGOrigin(target, endValue, 1, smooth, 0, this);\n\n              continue;\n            } else if (p in _rotationalProperties) {\n              _addRotationalPropTween(this, cache, p, startNum, relative ? _parseRelative(startNum, relative + endValue) : endValue);\n\n              continue;\n            } else if (p === \"smoothOrigin\") {\n              _addNonTweeningPT(this, cache, \"smooth\", cache.smooth, endValue);\n\n              continue;\n            } else if (p === \"force3D\") {\n              cache[p] = endValue;\n              continue;\n            } else if (p === \"transform\") {\n              _addRawTransformPTs(this, endValue, target);\n\n              continue;\n            }\n          } else if (!(p in style)) {\n            p = _checkPropPrefix(p) || p;\n          }\n\n          if (isTransformRelated || (endNum || endNum === 0) && (startNum || startNum === 0) && !_complexExp.test(endValue) && p in style) {\n            startUnit = (startValue + \"\").substr((startNum + \"\").length);\n            endNum || (endNum = 0);\n            endUnit = getUnit(endValue) || (p in _config.units ? _config.units[p] : startUnit);\n            startUnit !== endUnit && (startNum = _convertToUnit(target, p, startValue, endUnit));\n            this._pt = new PropTween(this._pt, isTransformRelated ? cache : style, p, startNum, (relative ? _parseRelative(startNum, relative + endNum) : endNum) - startNum, !isTransformRelated && (endUnit === \"px\" || p === \"zIndex\") && vars.autoRound !== false ? _renderRoundedCSSProp : _renderCSSProp);\n            this._pt.u = endUnit || 0;\n\n            if (startUnit !== endUnit && endUnit !== \"%\") {\n              this._pt.b = startValue;\n              this._pt.r = _renderCSSPropWithBeginning;\n            }\n          } else if (!(p in style)) {\n            if (p in target) {\n              this.add(target, p, startValue || target[p], relative ? relative + endValue : endValue, index, targets);\n            } else if (p !== \"parseTransform\") {\n              _missingPlugin(p, endValue);\n\n              continue;\n            }\n          } else {\n            _tweenComplexCSSString.call(this, target, p, startValue, relative ? relative + endValue : endValue);\n          }\n\n          isTransformRelated || (p in style ? inlineProps.push(p, 0, style[p]) : typeof target[p] === \"function\" ? inlineProps.push(p, 2, target[p]()) : inlineProps.push(p, 1, startValue || target[p]));\n          props.push(p);\n        }\n      }\n\n      hasPriority && _sortPropTweensByPriority(this);\n    },\n    render: function render(ratio, data) {\n      if (data.tween._time || !_reverting$1()) {\n        var pt = data._pt;\n\n        while (pt) {\n          pt.r(ratio, pt.d);\n          pt = pt._next;\n        }\n      } else {\n        data.styles.revert();\n      }\n    },\n    get: _get,\n    aliases: _propertyAliases,\n    getSetter: function getSetter(target, property, plugin) {\n      var p = _propertyAliases[property];\n      p && p.indexOf(\",\") < 0 && (property = p);\n      return property in _transformProps && property !== _transformOriginProp && (target._gsap.x || _get(target, \"x\")) ? plugin && _recentSetterPlugin === plugin ? property === \"scale\" ? _setterScale : _setterTransform : (_recentSetterPlugin = plugin || {}) && (property === \"scale\" ? _setterScaleWithRender : _setterTransformWithRender) : target.style && !_isUndefined(target.style[property]) ? _setterCSSStyle : ~property.indexOf(\"-\") ? _setterCSSProp : _getSetter(target, property);\n    },\n    core: {\n      _removeProperty: _removeProperty,\n      _getMatrix: _getMatrix\n    }\n  };\n  gsap.utils.checkPrefix = _checkPropPrefix;\n  gsap.core.getStyleSaver = _getStyleSaver;\n\n  (function (positionAndScale, rotation, others, aliases) {\n    var all = _forEachName(positionAndScale + \",\" + rotation + \",\" + others, function (name) {\n      _transformProps[name] = 1;\n    });\n\n    _forEachName(rotation, function (name) {\n      _config.units[name] = \"deg\";\n      _rotationalProperties[name] = 1;\n    });\n\n    _propertyAliases[all[13]] = positionAndScale + \",\" + rotation;\n\n    _forEachName(aliases, function (name) {\n      var split = name.split(\":\");\n      _propertyAliases[split[1]] = all[split[0]];\n    });\n  })(\"x,y,z,scale,scaleX,scaleY,xPercent,yPercent\", \"rotation,rotationX,rotationY,skewX,skewY\", \"transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective\", \"0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY\");\n\n  _forEachName(\"x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective\", function (name) {\n    _config.units[name] = \"px\";\n  });\n\n  gsap.registerPlugin(CSSPlugin);\n\n  var gsapWithCSS = gsap.registerPlugin(CSSPlugin) || gsap,\n      TweenMaxWithCSS = gsapWithCSS.core.Tween;\n\n  exports.Back = Back;\n  exports.Bounce = Bounce;\n  exports.CSSPlugin = CSSPlugin;\n  exports.Circ = Circ;\n  exports.Cubic = Cubic;\n  exports.Elastic = Elastic;\n  exports.Expo = Expo;\n  exports.Linear = Linear;\n  exports.Power0 = Power0;\n  exports.Power1 = Power1;\n  exports.Power2 = Power2;\n  exports.Power3 = Power3;\n  exports.Power4 = Power4;\n  exports.Quad = Quad;\n  exports.Quart = Quart;\n  exports.Quint = Quint;\n  exports.Sine = Sine;\n  exports.SteppedEase = SteppedEase;\n  exports.Strong = Strong;\n  exports.TimelineLite = Timeline;\n  exports.TimelineMax = Timeline;\n  exports.TweenLite = Tween;\n  exports.TweenMax = TweenMaxWithCSS;\n  exports.default = gsapWithCSS;\n  exports.gsap = gsapWithCSS;\n\n  if (typeof(window) === 'undefined' || window !== exports) {Object.defineProperty(exports, '__esModule', { value: true });} else {delete window.default;}\n\n})));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/gsap/dist/gsap.js\n");

/***/ })

};
;