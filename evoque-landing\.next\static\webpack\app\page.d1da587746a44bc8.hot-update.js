"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/animations/ParallaxBackground.tsx":
/*!**********************************************************!*\
  !*** ./src/components/animations/ParallaxBackground.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParallaxBackground: function() { return /* binding */ ParallaxBackground; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ParallaxBackground,default auto */ \n\nfunction ParallaxBackground(param) {\n    let { primaryColor = \"#cb997e\", secondaryColor = \"#5c8d89\", accentColor = \"#ddbea9\", density = \"medium\", weddingThemed = true, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 w-full h-full overflow-hidden pointer-events-none \".concat(className),\n        style: {\n            zIndex: -1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-30\",\n                style: {\n                    background: \"linear-gradient(135deg, \".concat(primaryColor, \"20 0%, \").concat(secondaryColor, \"20 50%, \").concat(accentColor, \"20 100%)\")\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            weddingThemed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10 w-4 h-4 rounded-full opacity-20\",\n                        style: {\n                            backgroundColor: primaryColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-20 w-3 h-3 rounded-full opacity-15\",\n                        style: {\n                            backgroundColor: secondaryColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-20 w-5 h-5 rounded-full opacity-10\",\n                        style: {\n                            backgroundColor: accentColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-10 right-10 w-2 h-2 rounded-full opacity-25\",\n                        style: {\n                            backgroundColor: primaryColor\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\ParallaxBackground.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = ParallaxBackground;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ParallaxBackground);\nvar _c;\n$RefreshReg$(_c, \"ParallaxBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/ParallaxBackground.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/animations/index.ts":
/*!********************************************!*\
  !*** ./src/components/animations/index.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedBackground: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.AnimatedBackground; },\n/* harmony export */   AnimatedButton: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.AnimatedButton; },\n/* harmony export */   AnimatedDivider: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.AnimatedDivider; },\n/* harmony export */   AnimatedIcon: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.AnimatedIcon; },\n/* harmony export */   AnimatedLogo: function() { return /* reexport safe */ _AnimatedLogo__WEBPACK_IMPORTED_MODULE_0__.AnimatedLogo; },\n/* harmony export */   ChatWidgetAnimation: function() { return /* reexport safe */ _ChatWidgetAnimation__WEBPACK_IMPORTED_MODULE_9__[\"default\"]; },\n/* harmony export */   Counter: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.Counter; },\n/* harmony export */   ElevateAnimation: function() { return /* reexport safe */ _ModuleAnimations__WEBPACK_IMPORTED_MODULE_3__.ElevateAnimation; },\n/* harmony export */   EngageAnimation: function() { return /* reexport safe */ _ModuleAnimations__WEBPACK_IMPORTED_MODULE_3__.EngageAnimation; },\n/* harmony export */   EnhanceAnimation: function() { return /* reexport safe */ _ModuleAnimations__WEBPACK_IMPORTED_MODULE_3__.EnhanceAnimation; },\n/* harmony export */   EvoqueLogoSvg: function() { return /* reexport safe */ _EvoqueLogoSvg__WEBPACK_IMPORTED_MODULE_1__.EvoqueLogoSvg; },\n/* harmony export */   FadeIn: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.FadeIn; },\n/* harmony export */   FeatureCard: function() { return /* reexport safe */ _FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   HoverCard: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.HoverCard; },\n/* harmony export */   Parallax: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.Parallax; },\n/* harmony export */   ParallaxBackground: function() { return /* reexport safe */ _ParallaxBackground__WEBPACK_IMPORTED_MODULE_7__.ParallaxBackground; },\n/* harmony export */   PulseButton: function() { return /* reexport safe */ _PulseButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   ScrollProgress: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.ScrollProgress; },\n/* harmony export */   StaggerContainer: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.StaggerContainer; },\n/* harmony export */   StaticBackgroundSection: function() { return /* reexport safe */ _StaticBackgroundSection__WEBPACK_IMPORTED_MODULE_8__.StaticBackgroundSection; },\n/* harmony export */   TextReveal: function() { return /* reexport safe */ _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__.TextReveal; },\n/* harmony export */   WeddingLogo: function() { return /* reexport safe */ _WeddingLogo__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _AnimatedLogo__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AnimatedLogo */ \"(app-pages-browser)/./src/components/animations/AnimatedLogo.tsx\");\n/* harmony import */ var _EvoqueLogoSvg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EvoqueLogoSvg */ \"(app-pages-browser)/./src/components/animations/EvoqueLogoSvg.tsx\");\n/* harmony import */ var _WeddingLogo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WeddingLogo */ \"(app-pages-browser)/./src/components/animations/WeddingLogo.tsx\");\n/* harmony import */ var _ModuleAnimations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ModuleAnimations */ \"(app-pages-browser)/./src/components/animations/ModuleAnimations.tsx\");\n/* harmony import */ var _FeatureCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FeatureCard */ \"(app-pages-browser)/./src/components/animations/FeatureCard.tsx\");\n/* harmony import */ var _GeneralAnimations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GeneralAnimations */ \"(app-pages-browser)/./src/components/animations/GeneralAnimations.tsx\");\n/* harmony import */ var _PulseButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PulseButton */ \"(app-pages-browser)/./src/components/animations/PulseButton.tsx\");\n/* harmony import */ var _ParallaxBackground__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ParallaxBackground */ \"(app-pages-browser)/./src/components/animations/ParallaxBackground.tsx\");\n/* harmony import */ var _StaticBackgroundSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StaticBackgroundSection */ \"(app-pages-browser)/./src/components/animations/StaticBackgroundSection.tsx\");\n/* harmony import */ var _ChatWidgetAnimation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ChatWidgetAnimation */ \"(app-pages-browser)/./src/components/animations/ChatWidgetAnimation.tsx\");\n// Export all animation components for easier imports\n// Logo animations\n\n\n\n// Module-specific animations\n\n\n// General animations\n\n\n// Background animations\n\n\n // New animation components\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/index.ts\n"));

/***/ })

});