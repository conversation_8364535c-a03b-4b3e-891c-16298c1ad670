# File generated from our OpenAPI spec by <PERSON>ainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable
from typing_extensions import Literal, Required, TypedDict

from .content_block import ContentBlock
from .text_block_param import Text<PERSON><PERSON>Param
from .image_block_param import Image<PERSON><PERSON><PERSON>aram
from .document_block_param import DocumentB<PERSON>Param
from .thinking_block_param import ThinkingB<PERSON>Param
from .tool_use_block_param import ToolU<PERSON><PERSON>lockParam
from .tool_result_block_param import ToolResultBlockParam
from .redacted_thinking_block_param import RedactedThinkingBlockParam

__all__ = ["MessageParam"]


class MessageParam(TypedDict, total=False):
    content: Required[
        Union[
            str,
            Iterable[
                Union[
                    TextBlockParam,
                    ImageBlockParam,
                    ToolUseBlockParam,
                    ToolResultBlockParam,
                    DocumentBlockParam,
                    ThinkingBlockParam,
                    RedactedThinkingBlockParam,
                    ContentBlock,
                ]
            ],
        ]
    ]

    role: Required[Literal["user", "assistant"]]
