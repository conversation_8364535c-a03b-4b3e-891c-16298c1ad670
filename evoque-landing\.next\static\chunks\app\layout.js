/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-sans%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-display%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cormorant_Garamond%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-serif%22%7D%5D%2C%22variableName%22%3A%22cormorant%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp%5Cglobals.css&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-sans%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-display%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cormorant_Garamond%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-serif%22%7D%5D%2C%22variableName%22%3A%22cormorant%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp%5Cglobals.css&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-sans\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-display\"}],\"variableName\":\"playfair\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-display\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-serif\"}],\"variableName\":\"cormorant\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cormorant_Garamond\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-serif\\\"}],\\\"variableName\\\":\\\"cormorant\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-sans%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-display%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cormorant_Garamond%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-serif%22%7D%5D%2C%22variableName%22%3A%22cormorant%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp%5Cglobals.css&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"e1ecfdab907f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDE1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUxZWNmZGFiOTA3ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DOMAttributeNames: function() {\n        return DOMAttributeNames;\n    },\n    isEqualNode: function() {\n        return isEqualNode;\n    },\n    default: function() {\n        return initHeadManager;\n    }\n});\nconst DOMAttributeNames = {\n    acceptCharset: \"accept-charset\",\n    className: \"class\",\n    htmlFor: \"for\",\n    httpEquiv: \"http-equiv\",\n    noModule: \"noModule\"\n};\nfunction reactElementToDOM(param) {\n    let { type, props } = param;\n    const el = document.createElement(type);\n    for(const p in props){\n        if (!props.hasOwnProperty(p)) continue;\n        if (p === \"children\" || p === \"dangerouslySetInnerHTML\") continue;\n        // we don't render undefined props to the DOM\n        if (props[p] === undefined) continue;\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (type === \"script\" && (attr === \"async\" || attr === \"defer\" || attr === \"noModule\")) {\n            el[attr] = !!props[p];\n        } else {\n            el.setAttribute(attr, props[p]);\n        }\n    }\n    const { children, dangerouslySetInnerHTML } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute(\"nonce\");\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute(\"nonce\")) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute(\"nonce\", \"\");\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nlet updateElements;\nif (false) {} else {\n    updateElements = (type, components)=>{\n        const headEl = document.getElementsByTagName(\"head\")[0];\n        const headCountEl = headEl.querySelector(\"meta[name=next-head-count]\");\n        if (true) {\n            if (!headCountEl) {\n                console.error(\"Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing\");\n                return;\n            }\n        }\n        const headCount = Number(headCountEl.content);\n        const oldTags = [];\n        for(let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = (j == null ? void 0 : j.previousElementSibling) || null){\n            var _j_tagName;\n            if ((j == null ? void 0 : (_j_tagName = j.tagName) == null ? void 0 : _j_tagName.toLowerCase()) === type) {\n                oldTags.push(j);\n            }\n        }\n        const newTags = components.map(reactElementToDOM).filter((newTag)=>{\n            for(let k = 0, len = oldTags.length; k < len; k++){\n                const oldTag = oldTags[k];\n                if (isEqualNode(oldTag, newTag)) {\n                    oldTags.splice(k, 1);\n                    return false;\n                }\n            }\n            return true;\n        });\n        oldTags.forEach((t)=>{\n            var _t_parentNode;\n            return (_t_parentNode = t.parentNode) == null ? void 0 : _t_parentNode.removeChild(t);\n        });\n        newTags.forEach((t)=>headEl.insertBefore(t, headCountEl));\n        headCountEl.content = (headCount - oldTags.length + newTags.length).toString();\n    };\n}\nfunction initHeadManager() {\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const tags = {};\n            head.forEach((h)=>{\n                if (// it won't be inlined. In this case revert to the original behavior\n                h.type === \"link\" && h.props[\"data-optimized-fonts\"]) {\n                    if (document.querySelector('style[data-href=\"' + h.props[\"data-href\"] + '\"]')) {\n                        return;\n                    } else {\n                        h.props.href = h.props[\"data-href\"];\n                        h.props[\"data-href\"] = undefined;\n                    }\n                }\n                const components = tags[h.type] || [];\n                components.push(h);\n                tags[h.type] = components;\n            });\n            const titleComponent = tags.title ? tags.title[0] : null;\n            let title = \"\";\n            if (titleComponent) {\n                const { children } = titleComponent.props;\n                title = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            if (title !== document.title) document.title = title;\n            [\n                \"meta\",\n                \"base\",\n                \"link\",\n                \"style\",\n                \"script\"\n            ].forEach((type)=>{\n                updateElements(type, tags[type] || []);\n            });\n        }\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head-manager.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/head-manager.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    },\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleClientScriptLoad: function() {\n        return handleClientScriptLoad;\n    },\n    initScriptLoader: function() {\n        return initScriptLoader;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _headmanager = __webpack_require__(/*! ./head-manager */ \"(app-pages-browser)/./node_modules/next/dist/client/head-manager.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst ignoreProps = [\n    \"onLoad\",\n    \"onReady\",\n    \"dangerouslySetInnerHTML\",\n    \"children\",\n    \"onError\",\n    \"strategy\",\n    \"stylesheets\"\n];\nconst insertStylesheets = (stylesheets)=>{\n    // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n    //\n    // Using ReactDOM.preinit to feature detect appDir and inject styles\n    // Stylesheets might have already been loaded if initialized with Script component\n    // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n    // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n    if (_reactdom.default.preinit) {\n        stylesheets.forEach((stylesheet)=>{\n            _reactdom.default.preinit(stylesheet, {\n                as: \"style\"\n            });\n        });\n        return;\n    }\n    // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n    //\n    // We use this function to load styles when appdir is not detected\n    // TODO: Use React float APIs to load styles once available for pages dir\n    if (true) {\n        let head = document.head;\n        stylesheets.forEach((stylesheet)=>{\n            let link = document.createElement(\"link\");\n            link.type = \"text/css\";\n            link.rel = \"stylesheet\";\n            link.href = stylesheet;\n            head.appendChild(link);\n        });\n    }\n};\nconst loadScript = (props)=>{\n    const { src, id, onLoad = ()=>{}, onReady = null, dangerouslySetInnerHTML, children = \"\", strategy = \"afterInteractive\", onError, stylesheets } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement(\"script\");\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener(\"load\", function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener(\"error\", function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    for (const [k, value] of Object.entries(props)){\n        if (value === undefined || ignoreProps.includes(k)) {\n            continue;\n        }\n        const attr = _headmanager.DOMAttributeNames[k] || k.toLowerCase();\n        el.setAttribute(attr, value);\n    }\n    if (strategy === \"worker\") {\n        el.setAttribute(\"type\", \"text/partytown\");\n    }\n    el.setAttribute(\"data-nscript\", strategy);\n    // Load styles associated with this script\n    if (stylesheets) {\n        insertStylesheets(stylesheets);\n    }\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy = \"afterInteractive\" } = props;\n    if (strategy === \"lazyOnload\") {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === \"complete\") {\n        (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n    } else {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute(\"src\");\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\nfunction Script(props) {\n    const { id, src = \"\", onLoad = ()=>{}, onReady = null, strategy = \"afterInteractive\", onError, stylesheets, ...restProps } = props;\n    // Context is available only during SSR\n    const { updateScripts, scripts, getIsSsr, appDir, nonce } = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === \"afterInteractive\") {\n                loadScript(props);\n            } else if (strategy === \"lazyOnload\") {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === \"beforeInteractive\" || strategy === \"worker\") {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                {\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError,\n                    ...restProps\n                }\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n        // For other strategies injecting here ensures correct stylesheet order\n        // ReactDOM.preinit handles loading the styles in the correct order,\n        // also ensures the stylesheet is loaded only once and in a consistent manner\n        //\n        // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n        // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n        // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n        // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n        if (stylesheets) {\n            stylesheets.forEach((styleSrc)=>{\n                _reactdom.default.preinit(styleSrc, {\n                    as: \"style\"\n                });\n            });\n        }\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === \"beforeInteractive\") {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            0,\n                            {\n                                ...restProps\n                            }\n                        ]) + \")\"\n                    }\n                });\n            } else {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: \"script\",\n                    integrity: restProps.integrity\n                } : {\n                    as: \"script\"\n                });\n                return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            src\n                        ]) + \")\"\n                    }\n                });\n            }\n        } else if (strategy === \"afterInteractive\") {\n            if (src) {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: \"script\",\n                    integrity: restProps.integrity\n                } : {\n                    as: \"script\"\n                });\n            }\n        }\n    }\n    return null;\n}\n_c = Script;\nObject.defineProperty(Script, \"__nextScript\", {\n    value: true\n});\nconst _default = Script;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\nvar _c;\n$RefreshReg$(_c, \"Script\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={686:(e,r,t)=>{var n=t(808);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},808:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(686);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-serif\"}],\"variableName\":\"cormorant\"}":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Cormorant_Garamond","arguments":[{"subsets":["latin"],"weight":["300","400","500","600","700"],"display":"swap","variable":"--font-serif"}],"variableName":"cormorant"} ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Cormorant_Garamond_f2604b', '__Cormorant_Garamond_Fallback_f2604b'\",\"fontStyle\":\"normal\"},\"className\":\"__className_f2604b\",\"variable\":\"__variable_f2604b\"};\n    if(true) {\n      // 1749162637083\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkNvcm1vcmFudF9HYXJhbW9uZFwiLFwiYXJndW1lbnRzXCI6W3tcInN1YnNldHNcIjpbXCJsYXRpblwiXSxcIndlaWdodFwiOltcIjMwMFwiLFwiNDAwXCIsXCI1MDBcIixcIjYwMFwiLFwiNzAwXCJdLFwiZGlzcGxheVwiOlwic3dhcFwiLFwidmFyaWFibGVcIjpcIi0tZm9udC1zZXJpZlwifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImNvcm1vcmFudFwifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLDBHQUEwRztBQUNySSxPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBd0ksY0FBYyxzREFBc0Q7QUFDMU8sTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzPzhkMzkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ19fQ29ybW9yYW50X0dhcmFtb25kX2YyNjA0YicsICdfX0Nvcm1vcmFudF9HYXJhbW9uZF9GYWxsYmFja19mMjYwNGInXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZjI2MDRiXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV9mMjYwNGJcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc0OTE2MjYzNzA4M1xuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy9zbW9vdi9Eb3dubG9hZHMvRXZvcXVlIFdlZC9ldm9xdWUtbGFuZGluZy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cormorant_Garamond\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-serif\"}],\"variableName\":\"cormorant\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"}":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"display":"swap","variable":"--font-sans"}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1749162637026\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdLFwiZGlzcGxheVwiOlwic3dhcFwiLFwidmFyaWFibGVcIjpcIi0tZm9udC1zYW5zXCJ9XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyxnRkFBZ0Y7QUFDM0csT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQXdJLGNBQWMsc0RBQXNEO0FBQzFPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz83YmIzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidfX0ludGVyX2U4Y2UwYycsICdfX0ludGVyX0ZhbGxiYWNrX2U4Y2UwYydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlX2U4Y2UwY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ5MTYyNjM3MDI2XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL3Ntb292L0Rvd25sb2Fkcy9Fdm9xdWUgV2VkL2V2b3F1ZS1sYW5kaW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-display\"}],\"variableName\":\"playfair\"}":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Playfair_Display","arguments":[{"subsets":["latin"],"display":"swap","variable":"--font-display"}],"variableName":"playfair"} ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Playfair_Display_65f816', '__Playfair_Display_Fallback_65f816'\",\"fontStyle\":\"normal\"},\"className\":\"__className_65f816\",\"variable\":\"__variable_65f816\"};\n    if(true) {\n      // 1749162636377\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIlBsYXlmYWlyX0Rpc3BsYXlcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJkaXNwbGF5XCI6XCJzd2FwXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWRpc3BsYXlcIn1dLFwidmFyaWFibGVOYW1lXCI6XCJwbGF5ZmFpclwifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLHNHQUFzRztBQUNqSSxPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBd0ksY0FBYyxzREFBc0Q7QUFDMU8sTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzPzA3OTkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ19fUGxheWZhaXJfRGlzcGxheV82NWY4MTYnLCAnX19QbGF5ZmFpcl9EaXNwbGF5X0ZhbGxiYWNrXzY1ZjgxNidcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV82NWY4MTZcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlXzY1ZjgxNlwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ5MTYyNjM2Mzc3XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL3Ntb292L0Rvd25sb2Fkcy9Fdm9xdWUgV2VkL2V2b3F1ZS1sYW5kaW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-display\"}],\"variableName\":\"playfair\"}\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-sans%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-display%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cormorant_Garamond%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-serif%22%7D%5D%2C%22variableName%22%3A%22cormorant%22%7D&modules=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp%5Cglobals.css&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);