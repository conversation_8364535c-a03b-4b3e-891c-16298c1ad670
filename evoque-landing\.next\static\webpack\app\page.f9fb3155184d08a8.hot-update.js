"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/animations/advanced/RomanticFloatingElements.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/animations/advanced/RomanticFloatingElements.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RomanticFloatingElements; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst floatingVariants = {\n    float: {\n        y: [\n            0,\n            -20,\n            0\n        ],\n        transition: {\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n        }\n    },\n    rotate: {\n        rotate: [\n            0,\n            360\n        ],\n        transition: {\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n        }\n    },\n    pulse: {\n        scale: [\n            1,\n            1.1,\n            1\n        ],\n        opacity: [\n            0.7,\n            1,\n            0.7\n        ],\n        transition: {\n            duration: 3,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n        }\n    }\n};\nconst FloatingElement = (param)=>{\n    let { emoji, delay = 0, left, top, size = \"text-3xl\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"absolute \".concat(size, \" opacity-20\"),\n        style: {\n            left,\n            top\n        },\n        initial: {\n            opacity: 0,\n            scale: 0\n        },\n        animate: {\n            opacity: 0.2,\n            scale: 1\n        },\n        transition: {\n            delay,\n            duration: 1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            animate: [\n                \"float\",\n                \"rotate\"\n            ],\n            variants: floatingVariants,\n            children: emoji\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\advanced\\\\RomanticFloatingElements.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\advanced\\\\RomanticFloatingElements.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FloatingElement;\nfunction RomanticFloatingElements() {\n    const elements = [\n        {\n            emoji: \"\\uD83C\\uDF39\",\n            left: \"5%\",\n            top: \"10%\"\n        },\n        {\n            emoji: \"\\uD83D\\uDC8D\",\n            left: \"85%\",\n            top: \"15%\"\n        },\n        {\n            emoji: \"❤️\",\n            left: \"10%\",\n            top: \"70%\"\n        },\n        {\n            emoji: \"✨\",\n            left: \"90%\",\n            top: \"60%\"\n        },\n        {\n            emoji: \"\\uD83E\\uDD8B\",\n            left: \"15%\",\n            top: \"40%\"\n        },\n        {\n            emoji: \"\\uD83C\\uDF38\",\n            left: \"75%\",\n            top: \"80%\"\n        },\n        {\n            emoji: \"\\uD83D\\uDC90\",\n            left: \"50%\",\n            top: \"20%\"\n        },\n        {\n            emoji: \"\\uD83D\\uDD4A️\",\n            left: \"30%\",\n            top: \"85%\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n        children: elements.map((element, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingElement, {\n                emoji: element.emoji,\n                left: element.left,\n                top: element.top,\n                delay: index * 0.2\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\advanced\\\\RomanticFloatingElements.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\components\\\\animations\\\\advanced\\\\RomanticFloatingElements.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_c1 = RomanticFloatingElements;\nvar _c, _c1;\n$RefreshReg$(_c, \"FloatingElement\");\n$RefreshReg$(_c1, \"RomanticFloatingElements\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FuaW1hdGlvbnMvYWR2YW5jZWQvUm9tYW50aWNGbG9hdGluZ0VsZW1lbnRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFDYTtBQUV2QyxNQUFNRSxtQkFBbUI7SUFDdkJDLE9BQU87UUFDTEMsR0FBRztZQUFDO1lBQUcsQ0FBQztZQUFJO1NBQUU7UUFDZEMsWUFBWTtZQUNWQyxVQUFVO1lBQ1ZDLFFBQVFDO1lBQ1JDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLFFBQVE7UUFDTkEsUUFBUTtZQUFDO1lBQUc7U0FBSTtRQUNoQkwsWUFBWTtZQUNWQyxVQUFVO1lBQ1ZDLFFBQVFDO1lBQ1JDLE1BQU07UUFDUjtJQUNGO0lBQ0FFLE9BQU87UUFDTEMsT0FBTztZQUFDO1lBQUc7WUFBSztTQUFFO1FBQ2xCQyxTQUFTO1lBQUM7WUFBSztZQUFHO1NBQUk7UUFDdEJSLFlBQVk7WUFDVkMsVUFBVTtZQUNWQyxRQUFRQztZQUNSQyxNQUFNO1FBQ1I7SUFDRjtBQUNGO0FBVUEsTUFBTUssa0JBQWtEO1FBQUMsRUFDdkRDLEtBQUssRUFDTEMsUUFBUSxDQUFDLEVBQ1RDLElBQUksRUFDSkMsR0FBRyxFQUNIQyxPQUFPLFVBQVUsRUFDbEI7SUFDQyxxQkFDRSw4REFBQ2xCLGlEQUFNQSxDQUFDbUIsR0FBRztRQUNUQyxXQUFXLFlBQWlCLE9BQUxGLE1BQUs7UUFDNUJHLE9BQU87WUFBRUw7WUFBTUM7UUFBSTtRQUNuQkssU0FBUztZQUFFVixTQUFTO1lBQUdELE9BQU87UUFBRTtRQUNoQ1ksU0FBUztZQUFFWCxTQUFTO1lBQUtELE9BQU87UUFBRTtRQUNsQ1AsWUFBWTtZQUFFVztZQUFPVixVQUFVO1FBQUU7a0JBRWpDLDRFQUFDTCxpREFBTUEsQ0FBQ21CLEdBQUc7WUFDVEksU0FBUztnQkFBQztnQkFBUzthQUFTO1lBQzVCQyxVQUFVdkI7c0JBRVRhOzs7Ozs7Ozs7OztBQUlUO0tBdkJNRDtBQXlCUyxTQUFTWTtJQUN0QixNQUFNQyxXQUFXO1FBQ2Y7WUFBRVosT0FBTztZQUFNRSxNQUFNO1lBQU1DLEtBQUs7UUFBTTtRQUN0QztZQUFFSCxPQUFPO1lBQU1FLE1BQU07WUFBT0MsS0FBSztRQUFNO1FBQ3ZDO1lBQUVILE9BQU87WUFBTUUsTUFBTTtZQUFPQyxLQUFLO1FBQU07UUFDdkM7WUFBRUgsT0FBTztZQUFLRSxNQUFNO1lBQU9DLEtBQUs7UUFBTTtRQUN0QztZQUFFSCxPQUFPO1lBQU1FLE1BQU07WUFBT0MsS0FBSztRQUFNO1FBQ3ZDO1lBQUVILE9BQU87WUFBTUUsTUFBTTtZQUFPQyxLQUFLO1FBQU07UUFDdkM7WUFBRUgsT0FBTztZQUFNRSxNQUFNO1lBQU9DLEtBQUs7UUFBTTtRQUN2QztZQUFFSCxPQUFPO1lBQU9FLE1BQU07WUFBT0MsS0FBSztRQUFNO0tBQ3pDO0lBRUQscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7a0JBQ1pNLFNBQVNDLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDdEIsOERBQUNoQjtnQkFFQ0MsT0FBT2MsUUFBUWQsS0FBSztnQkFDcEJFLE1BQU1ZLFFBQVFaLElBQUk7Z0JBQ2xCQyxLQUFLVyxRQUFRWCxHQUFHO2dCQUNoQkYsT0FBT2MsUUFBUTtlQUpWQTs7Ozs7Ozs7OztBQVNmO01BekJ3QkoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvYW5pbWF0aW9ucy9hZHZhbmNlZC9Sb21hbnRpY0Zsb2F0aW5nRWxlbWVudHMudHN4Pzk4ZGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5cbmNvbnN0IGZsb2F0aW5nVmFyaWFudHMgPSB7XG4gIGZsb2F0OiB7XG4gICAgeTogWzAsIC0yMCwgMF0sXG4gICAgdHJhbnNpdGlvbjoge1xuICAgICAgZHVyYXRpb246IDYsXG4gICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgZWFzZTogJ2Vhc2VJbk91dCcsXG4gICAgfSxcbiAgfSxcbiAgcm90YXRlOiB7XG4gICAgcm90YXRlOiBbMCwgMzYwXSxcbiAgICB0cmFuc2l0aW9uOiB7XG4gICAgICBkdXJhdGlvbjogMjAsXG4gICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgZWFzZTogJ2xpbmVhcicsXG4gICAgfSxcbiAgfSxcbiAgcHVsc2U6IHtcbiAgICBzY2FsZTogWzEsIDEuMSwgMV0sXG4gICAgb3BhY2l0eTogWzAuNywgMSwgMC43XSxcbiAgICB0cmFuc2l0aW9uOiB7XG4gICAgICBkdXJhdGlvbjogMyxcbiAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICBlYXNlOiAnZWFzZUluT3V0JyxcbiAgICB9LFxuICB9LFxufTtcblxuaW50ZXJmYWNlIEZsb2F0aW5nRWxlbWVudFByb3BzIHtcbiAgZW1vamk6IHN0cmluZztcbiAgZGVsYXk/OiBudW1iZXI7XG4gIGxlZnQ/OiBzdHJpbmc7XG4gIHRvcD86IHN0cmluZztcbiAgc2l6ZT86IHN0cmluZztcbn1cblxuY29uc3QgRmxvYXRpbmdFbGVtZW50OiBSZWFjdC5GQzxGbG9hdGluZ0VsZW1lbnRQcm9wcz4gPSAoe1xuICBlbW9qaSxcbiAgZGVsYXkgPSAwLFxuICBsZWZ0LFxuICB0b3AsXG4gIHNpemUgPSAndGV4dC0zeGwnLFxufSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSAke3NpemV9IG9wYWNpdHktMjBgfVxuICAgICAgc3R5bGU9e3sgbGVmdCwgdG9wIH19XG4gICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwIH19XG4gICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDAuMiwgc2NhbGU6IDEgfX1cbiAgICAgIHRyYW5zaXRpb249e3sgZGVsYXksIGR1cmF0aW9uOiAxIH19XG4gICAgPlxuICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgYW5pbWF0ZT17WydmbG9hdCcsICdyb3RhdGUnXX1cbiAgICAgICAgdmFyaWFudHM9e2Zsb2F0aW5nVmFyaWFudHN9XG4gICAgICA+XG4gICAgICAgIHtlbW9qaX1cbiAgICAgIDwvbW90aW9uLmRpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb21hbnRpY0Zsb2F0aW5nRWxlbWVudHMoKSB7XG4gIGNvbnN0IGVsZW1lbnRzID0gW1xuICAgIHsgZW1vamk6ICfwn4y5JywgbGVmdDogJzUlJywgdG9wOiAnMTAlJyB9LFxuICAgIHsgZW1vamk6ICfwn5KNJywgbGVmdDogJzg1JScsIHRvcDogJzE1JScgfSxcbiAgICB7IGVtb2ppOiAn4p2k77iPJywgbGVmdDogJzEwJScsIHRvcDogJzcwJScgfSxcbiAgICB7IGVtb2ppOiAn4pyoJywgbGVmdDogJzkwJScsIHRvcDogJzYwJScgfSxcbiAgICB7IGVtb2ppOiAn8J+miycsIGxlZnQ6ICcxNSUnLCB0b3A6ICc0MCUnIH0sXG4gICAgeyBlbW9qaTogJ/CfjLgnLCBsZWZ0OiAnNzUlJywgdG9wOiAnODAlJyB9LFxuICAgIHsgZW1vamk6ICfwn5KQJywgbGVmdDogJzUwJScsIHRvcDogJzIwJScgfSxcbiAgICB7IGVtb2ppOiAn8J+Viu+4jycsIGxlZnQ6ICczMCUnLCB0b3A6ICc4NSUnIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcG9pbnRlci1ldmVudHMtbm9uZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHtlbGVtZW50cy5tYXAoKGVsZW1lbnQsIGluZGV4KSA9PiAoXG4gICAgICAgIDxGbG9hdGluZ0VsZW1lbnRcbiAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgIGVtb2ppPXtlbGVtZW50LmVtb2ppfVxuICAgICAgICAgIGxlZnQ9e2VsZW1lbnQubGVmdH1cbiAgICAgICAgICB0b3A9e2VsZW1lbnQudG9wfVxuICAgICAgICAgIGRlbGF5PXtpbmRleCAqIDAuMn1cbiAgICAgICAgLz5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsIm1vdGlvbiIsImZsb2F0aW5nVmFyaWFudHMiLCJmbG9hdCIsInkiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJyZXBlYXQiLCJJbmZpbml0eSIsImVhc2UiLCJyb3RhdGUiLCJwdWxzZSIsInNjYWxlIiwib3BhY2l0eSIsIkZsb2F0aW5nRWxlbWVudCIsImVtb2ppIiwiZGVsYXkiLCJsZWZ0IiwidG9wIiwic2l6ZSIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwiaW5pdGlhbCIsImFuaW1hdGUiLCJ2YXJpYW50cyIsIlJvbWFudGljRmxvYXRpbmdFbGVtZW50cyIsImVsZW1lbnRzIiwibWFwIiwiZWxlbWVudCIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/animations/advanced/RomanticFloatingElements.tsx\n"));

/***/ })

});