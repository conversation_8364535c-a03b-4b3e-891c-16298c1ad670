"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.webmanifest/route";
exports.ids = ["app/manifest.webmanifest/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/@vercel/og/index.node.js":
/*!**************************************************************!*\
  !*** external "next/dist/compiled/@vercel/og/index.node.js" ***!
  \**************************************************************/
/***/ ((module) => {

module.exports = import("next/dist/compiled/@vercel/og/index.node.js");;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_metadata_route_loader_page_2Fmanifest_webmanifest_2Froute_isDynamic_1_C_Users_smoov_Downloads_Evoque_Wed_evoque_landing_src_app_manifest_ts_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!./src/app/manifest.ts?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!./src/app/manifest.ts?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/manifest.webmanifest/route\",\n        pathname: \"/manifest.webmanifest\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.webmanifest/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\manifest.ts?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Fmanifest_webmanifest_2Froute_isDynamic_1_C_Users_smoov_Downloads_Evoque_Wed_evoque_landing_src_app_manifest_ts_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/manifest.webmanifest/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./src/app/manifest.ts":
/*!*****************************!*\
  !*** ./src/app/manifest.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ manifest)\n/* harmony export */ });\nfunction manifest() {\n    return {\n        name: \"Evoque - AI-Powered Wedding Venue Management\",\n        short_name: \"Evoque\",\n        description: \"AI-powered wedding venue management platform\",\n        start_url: \"/\",\n        display: \"standalone\",\n        background_color: \"#ffffff\",\n        theme_color: \"#cb997e\",\n        icons: [\n            {\n                src: \"/favicon.ico\",\n                sizes: \"48x48\",\n                type: \"image/x-icon\"\n            },\n            // Using favicon.ico as a fallback since other icons are missing\n            {\n                src: \"/favicon.ico\",\n                sizes: \"192x192\",\n                type: \"image/x-icon\"\n            },\n            {\n                src: \"/favicon.ico\",\n                sizes: \"512x512\",\n                type: \"image/x-icon\"\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1tZXRhZGF0YS1yb3V0ZSkvLi9zcmMvYXBwL21hbmlmZXN0LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFZSxTQUFTQTtJQUN0QixPQUFPO1FBQ0xDLE1BQU07UUFDTkMsWUFBWTtRQUNaQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFDYkMsT0FBTztZQUNMO2dCQUNFQyxLQUFLO2dCQUNMQyxPQUFPO2dCQUNQQyxNQUFNO1lBQ1I7WUFDQSxnRUFBZ0U7WUFDaEU7Z0JBQ0VGLEtBQUs7Z0JBQ0xDLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO2dCQUNFRixLQUFLO2dCQUNMQyxPQUFPO2dCQUNQQyxNQUFNO1lBQ1I7U0FDRDtJQUNIO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ldm9xdWUtbGFuZGluZy8uL3NyYy9hcHAvbWFuaWZlc3QudHM/YzY5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNZXRhZGF0YVJvdXRlIH0gZnJvbSAnbmV4dCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYW5pZmVzdCgpOiBNZXRhZGF0YVJvdXRlLk1hbmlmZXN0IHtcclxuICByZXR1cm4ge1xyXG4gICAgbmFtZTogJ0V2b3F1ZSAtIEFJLVBvd2VyZWQgV2VkZGluZyBWZW51ZSBNYW5hZ2VtZW50JyxcclxuICAgIHNob3J0X25hbWU6ICdFdm9xdWUnLFxyXG4gICAgZGVzY3JpcHRpb246ICdBSS1wb3dlcmVkIHdlZGRpbmcgdmVudWUgbWFuYWdlbWVudCBwbGF0Zm9ybScsXHJcbiAgICBzdGFydF91cmw6ICcvJyxcclxuICAgIGRpc3BsYXk6ICdzdGFuZGFsb25lJyxcclxuICAgIGJhY2tncm91bmRfY29sb3I6ICcjZmZmZmZmJyxcclxuICAgIHRoZW1lX2NvbG9yOiAnI2NiOTk3ZScsXHJcbiAgICBpY29uczogW1xyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiAnL2Zhdmljb24uaWNvJyxcclxuICAgICAgICBzaXplczogJzQ4eDQ4JyxcclxuICAgICAgICB0eXBlOiAnaW1hZ2UveC1pY29uJyxcclxuICAgICAgfSxcclxuICAgICAgLy8gVXNpbmcgZmF2aWNvbi5pY28gYXMgYSBmYWxsYmFjayBzaW5jZSBvdGhlciBpY29ucyBhcmUgbWlzc2luZ1xyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiAnL2Zhdmljb24uaWNvJyxcclxuICAgICAgICBzaXplczogJzE5MngxOTInLFxyXG4gICAgICAgIHR5cGU6ICdpbWFnZS94LWljb24nLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiAnL2Zhdmljb24uaWNvJyxcclxuICAgICAgICBzaXplczogJzUxMng1MTInLFxyXG4gICAgICAgIHR5cGU6ICdpbWFnZS94LWljb24nLFxyXG4gICAgICB9LFxyXG4gICAgXSxcclxuICB9O1xyXG59ICJdLCJuYW1lcyI6WyJtYW5pZmVzdCIsIm5hbWUiLCJzaG9ydF9uYW1lIiwiZGVzY3JpcHRpb24iLCJzdGFydF91cmwiLCJkaXNwbGF5IiwiYmFja2dyb3VuZF9jb2xvciIsInRoZW1lX2NvbG9yIiwiaWNvbnMiLCJzcmMiLCJzaXplcyIsInR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-metadata-route)/./src/app/manifest.ts\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!./src/app/manifest.ts?__next_metadata_route__":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!./src/app/manifest.ts?__next_metadata_route__ ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var C_Users_smoov_Downloads_Evoque_Wed_evoque_landing_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/app/manifest.ts */ \"(app-metadata-route)/./src/app/manifest.ts\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/webpack/loaders/metadata/resolve-route-data */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/metadata/resolve-route-data.js\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst contentType = \"application/manifest+json\"\nconst fileType = \"manifest\"\n\nasync function GET() {\n  const data = await (0,C_Users_smoov_Downloads_Evoque_Wed_evoque_landing_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()\n  const content = (0,next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__.resolveRouteData)(data, fileType)\n\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(content, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1tZXRhZGF0YS1yb3V0ZSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLXJvdXRlLWxvYWRlci5qcz9wYWdlPSUyRm1hbmlmZXN0LndlYm1hbmlmZXN0JTJGcm91dGUmaXNEeW5hbWljPTEhLi9zcmMvYXBwL21hbmlmZXN0LnRzP19fbmV4dF9tZXRhZGF0YV9yb3V0ZV9fIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUMwRDtBQUNOOztBQUU5RjtBQUNBOztBQUVPO0FBQ1AscUJBQXFCLGlIQUFPO0FBQzVCLGtCQUFrQiw2R0FBZ0I7O0FBRWxDLGFBQWEscURBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2V2b3F1ZS1sYW5kaW5nLy4vc3JjL2FwcC9tYW5pZmVzdC50cz9hOGM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IGhhbmRsZXIgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXHNtb292XFxcXERvd25sb2Fkc1xcXFxFdm9xdWUgV2VkXFxcXGV2b3F1ZS1sYW5kaW5nXFxcXHNyY1xcXFxhcHBcXFxcbWFuaWZlc3QudHNcIlxuaW1wb3J0IHsgcmVzb2x2ZVJvdXRlRGF0YSB9IGZyb20gJ25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbWV0YWRhdGEvcmVzb2x2ZS1yb3V0ZS1kYXRhJ1xuXG5jb25zdCBjb250ZW50VHlwZSA9IFwiYXBwbGljYXRpb24vbWFuaWZlc3QranNvblwiXG5jb25zdCBmaWxlVHlwZSA9IFwibWFuaWZlc3RcIlxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICBjb25zdCBkYXRhID0gYXdhaXQgaGFuZGxlcigpXG4gIGNvbnN0IGNvbnRlbnQgPSByZXNvbHZlUm91dGVEYXRhKGRhdGEsIGZpbGVUeXBlKVxuXG4gIHJldHVybiBuZXcgTmV4dFJlc3BvbnNlKGNvbnRlbnQsIHtcbiAgICBoZWFkZXJzOiB7XG4gICAgICAnQ29udGVudC1UeXBlJzogY29udGVudFR5cGUsXG4gICAgICAnQ2FjaGUtQ29udHJvbCc6IFwicHVibGljLCBtYXgtYWdlPTAsIG11c3QtcmV2YWxpZGF0ZVwiLFxuICAgIH0sXG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.webmanifest%2Froute&isDynamic=1!./src/app/manifest.ts?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csmoov%5CDownloads%5CEvoque%20Wed%5Cevoque-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();