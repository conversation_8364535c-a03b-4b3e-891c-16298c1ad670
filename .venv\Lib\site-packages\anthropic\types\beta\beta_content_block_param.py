# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .beta_text_block_param import BetaTextBlockParam
from .beta_image_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON>Param
from .beta_thinking_block_param import BetaT<PERSON>king<PERSON><PERSON><PERSON>aram
from .beta_tool_use_block_param import <PERSON>T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ara<PERSON>
from .beta_base64_pdf_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Param
from .beta_tool_result_block_param import <PERSON><PERSON>ool<PERSON>esult<PERSON>lockParam
from .beta_redacted_thinking_block_param import BetaRedactedThinking<PERSON>lockParam

__all__ = ["BetaContentBlockParam"]

BetaContentBlockParam: TypeAlias = Union[
    BetaTextBlockParam,
    BetaImageBlockParam,
    BetaToolUseBlockParam,
    BetaToolResultBlockParam,
    BetaBase64PDFBlockParam,
    BetaThinkingBlockParam,
    BetaRedactedThinkingBlockParam,
]
