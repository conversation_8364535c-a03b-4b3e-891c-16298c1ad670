"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ContactForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ContactForm */ \"(app-pages-browser)/./src/components/ContactForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Import only essential components\n\nfunction HomePage() {\n    _s();\n    const { scrollY, scrollYProgress } = useScroll();\n    const opacity = useTransform(scrollYProgress, [\n        0,\n        0.2\n    ], [\n        1,\n        0\n    ]);\n    const [isLoaded, setIsLoaded] = useState(false);\n    const [videoLoaded, setVideoLoaded] = useState(false);\n    const [videoError, setVideoError] = useState(false);\n    const videoRef = useRef(null);\n    const containerRef = useRef(null);\n    // Parallax transforms for video effect\n    const heroY = useTransform(scrollY, [\n        0,\n        1000\n    ], [\n        0,\n        -500\n    ]);\n    const videoOpacity = useTransform(scrollY, [\n        200,\n        500,\n        800,\n        1200\n    ], [\n        0,\n        1,\n        1,\n        0\n    ]);\n    const videoScale = useTransform(scrollY, [\n        0,\n        1000\n    ], [\n        1.1,\n        1\n    ]);\n    useEffect(()=>{\n        setIsLoaded(true);\n        // Initialize GSAP animations for video reveal\n        const ctx = gsap.context(()=>{\n            gsap.timeline({\n                scrollTrigger: {\n                    trigger: \".hero-section\",\n                    start: \"top top\",\n                    end: \"bottom top\",\n                    scrub: true\n                }\n            }).to(\".hero-content\", {\n                y: -200,\n                opacity: 0.3,\n                ease: \"power2.inOut\"\n            });\n        }, containerRef);\n        return ()=>ctx.revert();\n    }, []);\n    const handleVideoLoad = ()=>{\n        setVideoLoaded(true);\n        if (videoRef.current) {\n            videoRef.current.play().catch(()=>{\n                setVideoError(true);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimationWrapper, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            ref: containerRef,\n            className: \"bg-white min-h-screen relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RomanticFloatingElements, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.section, {\n                    className: \"hero-section min-h-screen flex items-center relative z-20 bg-gradient-to-br from-primary-50 via-white to-secondary-50\",\n                    style: {\n                        y: heroY\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-content container mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                className: \"text-center max-w-4xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatePresence, {\n                                    children: isLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    type: \"spring\",\n                                                    stiffness: 100,\n                                                    delay: 0.2\n                                                },\n                                                className: \"mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MorphingWeddingLogo, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                                text: \"Transform Your Venue into a Love Story\",\n                                                className: \"text-5xl md:text-7xl font-playfair font-bold text-primary-800 mb-6 leading-tight\",\n                                                variant: \"romantic\",\n                                                delay: 0.4\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.p, {\n                                                className: \"text-xl md:text-2xl text-gray-600 mb-8 font-light\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.8\n                                                },\n                                                children: \"AI-Powered Marketing That Connects Hearts & Venues\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"#demo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.button, {\n                                                            className: \"px-8 py-4 bg-primary-600 text-white rounded-full font-medium shadow-lg hover:shadow-xl transform transition-all duration-300\",\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"#b8866f\"\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: \"Watch Your Venue Come Alive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"#contact\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.button, {\n                                                            className: \"px-8 py-4 bg-white text-primary-600 rounded-full font-medium shadow-lg hover:shadow-xl transform transition-all duration-300 border-2 border-primary-600\",\n                                                            whileHover: {\n                                                                scale: 1.05,\n                                                                backgroundColor: \"#fff5f0\"\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: \"Start Free Trial\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                            className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2\",\n                            animate: {\n                                y: [\n                                    0,\n                                    10,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                repeat: Infinity,\n                                duration: 2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    className: \"w-1 h-3 bg-primary-400 rounded-full mt-2\",\n                                    animate: {\n                                        opacity: [\n                                            0,\n                                            1,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        repeat: Infinity,\n                                        duration: 1.5\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"video-section relative h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                        className: \"sticky top-0 w-full h-screen overflow-hidden\",\n                        style: {\n                            opacity: videoOpacity,\n                            scale: videoScale\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    ref: videoRef,\n                                    className: \"w-full h-full object-cover\",\n                                    src: \"/videos/WeddingAgencyVideo.mp4\",\n                                    muted: true,\n                                    loop: true,\n                                    playsInline: true,\n                                    onLoadedData: handleVideoLoad,\n                                    onError: ()=>setVideoError(true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                !videoLoaded && !videoError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 border-4 border-primary-300 border-t-primary-600 rounded-full animate-spin mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600 text-lg\",\n                                                children: \"Loading your venue experience...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center z-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"text-center text-white px-4\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 1,\n                                            delay: 0.5\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl font-playfair mb-4 drop-shadow-2xl\",\n                                                children: \"Where Dreams Become Reality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl font-light drop-shadow-xl\",\n                                                children: \"Showcase your venue like never before\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollRevealSection, {\n                    variant: \"elegant\",\n                    className: \"relative z-30 bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                    text: \"AI-Powered Features for Modern Venues\",\n                                    className: \"text-4xl md:text-6xl font-playfair text-center text-primary-800 mb-16\",\n                                    variant: \"slideIn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"24/7 AI Concierge\",\n                                            description: \"Never miss a lead with intelligent responses that capture hearts\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-primary-400 to-secondary-400\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"Smart Lead Qualification\",\n                                            description: \"Automatically identify and prioritize your perfect couples\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-secondary-400 to-accent-400\",\n                                            delay: 0.4\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PremiumFeatureCard, {\n                                            title: \"Venue Analytics\",\n                                            description: \"Track what couples love about your venue and optimize\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                }, void 0, false, void 0, void 0)\n                                            }, void 0, false, void 0, void 0),\n                                            gradient: \"from-accent-400 to-primary-400\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollRevealSection, {\n                    variant: \"bloom\",\n                    className: \"py-20 bg-gradient-to-b from-gray-50 to-white relative z-30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElegantTextReveal, {\n                                text: \"Why Venues Choose Evoque\",\n                                variant: \"romantic\",\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-12 text-gray-800\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    {\n                                        value: \"72%\",\n                                        text: \"of couples book venues that respond within 24 hours\",\n                                        color: \"#cb997e\",\n                                        icon: \"⏱️\",\n                                        animation: \"heart-pulse\"\n                                    },\n                                    {\n                                        value: \"89%\",\n                                        text: \"of Gen Z couples research venues online first\",\n                                        color: \"#5c8d89\",\n                                        icon: \"\\uD83D\\uDCBB\",\n                                        animation: \"sparkle-burst\"\n                                    },\n                                    {\n                                        value: \"3.2x\",\n                                        text: \"higher booking rates with automated responses\",\n                                        color: \"#cb997e\",\n                                        icon: \"\\uD83D\\uDCC8\",\n                                        animation: \"wedding-rings-sparkle\"\n                                    },\n                                    {\n                                        value: \"68%\",\n                                        text: \"of venues lose bookings due to slow response times\",\n                                        color: \"#5c8d89\",\n                                        icon: \"\\uD83D\\uDCC9\",\n                                        animation: \"floral-bloom\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GSAPScrollAnimation, {\n                                        animation: \"fadeInUp\",\n                                        delay: index * 0.1,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                            className: \"relative bg-white p-8 rounded-2xl shadow-lg overflow-hidden group\",\n                                            whileHover: {\n                                                y: -5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 opacity-20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LottieAnimation, {\n                                                        animationPath: \"/animations/\".concat(stat.animation, \".json\"),\n                                                        width: 80,\n                                                        height: 80,\n                                                        loop: true,\n                                                        autoplay: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-2\",\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            style: {\n                                                                color: stat.color\n                                                            },\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: stat.text\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                    className: \"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-300\",\n                                                    style: {\n                                                        background: \"linear-gradient(135deg, \".concat(stat.color, \"20 0%, transparent 100%)\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WidgetDemoSection, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    id: \"demo\",\n                    className: \"py-20 bg-gradient-to-b from-white to-primary-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-12\",\n                                children: \"See Evoque in Action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold mb-4\",\n                                                children: \"Watch How We Transform Venue Marketing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-6\",\n                                                children: \"Experience firsthand how our AI-powered platform can revolutionize your venue's online presence and booking process.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"See real conversations with engaged couples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Watch automated tour scheduling in action\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-primary-600 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Explore our analytics dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-video bg-gray-200 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Demo Video Placeholder\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VenueImportGuide, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomizableChatWidget, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Evoque Wed\\\\evoque-landing\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"IlmdxRfawN4WmRCoHNJ53rdqpbk=\", true);\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});