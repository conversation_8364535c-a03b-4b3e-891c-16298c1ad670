"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lottie-react";
exports.ids = ["vendor-chunks/lottie-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/lottie-react/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/lottie-react/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar lottie = __webpack_require__(/*! lottie-web */ \"(ssr)/./node_modules/lottie-web/build/player/lottie.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar lottie__default = /*#__PURE__*/_interopDefaultLegacy(lottie);\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var s = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nvar _excluded$1 = [\"animationData\", \"loop\", \"autoplay\", \"initialSegment\", \"onComplete\", \"onLoopComplete\", \"onEnterFrame\", \"onSegmentStart\", \"onConfigReady\", \"onDataReady\", \"onDataFailed\", \"onLoadedImages\", \"onDOMLoaded\", \"onDestroy\", \"lottieRef\", \"renderer\", \"name\", \"assetsPath\", \"rendererSettings\"];\nvar useLottie = function useLottie(props, style) {\n  var animationData = props.animationData,\n    loop = props.loop,\n    autoplay = props.autoplay,\n    initialSegment = props.initialSegment,\n    onComplete = props.onComplete,\n    onLoopComplete = props.onLoopComplete,\n    onEnterFrame = props.onEnterFrame,\n    onSegmentStart = props.onSegmentStart,\n    onConfigReady = props.onConfigReady,\n    onDataReady = props.onDataReady,\n    onDataFailed = props.onDataFailed,\n    onLoadedImages = props.onLoadedImages,\n    onDOMLoaded = props.onDOMLoaded,\n    onDestroy = props.onDestroy;\n    props.lottieRef;\n    props.renderer;\n    props.name;\n    props.assetsPath;\n    props.rendererSettings;\n    var rest = _objectWithoutProperties(props, _excluded$1);\n  var _useState = React.useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    animationLoaded = _useState2[0],\n    setAnimationLoaded = _useState2[1];\n  var animationInstanceRef = React.useRef();\n  var animationContainer = React.useRef(null);\n  /*\n        ======================================\n            INTERACTION METHODS\n        ======================================\n     */\n  /**\n   * Play\n   */\n  var play = function play() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.play();\n  };\n  /**\n   * Stop\n   */\n  var stop = function stop() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.stop();\n  };\n  /**\n   * Pause\n   */\n  var pause = function pause() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.pause();\n  };\n  /**\n   * Set animation speed\n   * @param speed\n   */\n  var setSpeed = function setSpeed(speed) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSpeed(speed);\n  };\n  /**\n   * Got to frame and play\n   * @param value\n   * @param isFrame\n   */\n  var goToAndPlay = function goToAndPlay(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndPlay(value, isFrame);\n  };\n  /**\n   * Got to frame and stop\n   * @param value\n   * @param isFrame\n   */\n  var goToAndStop = function goToAndStop(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(value, isFrame);\n  };\n  /**\n   * Set animation direction\n   * @param direction\n   */\n  var setDirection = function setDirection(direction) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setDirection(direction);\n  };\n  /**\n   * Play animation segments\n   * @param segments\n   * @param forceFlag\n   */\n  var playSegments = function playSegments(segments, forceFlag) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.playSegments(segments, forceFlag);\n  };\n  /**\n   * Set sub frames\n   * @param useSubFrames\n   */\n  var setSubframe = function setSubframe(useSubFrames) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSubframe(useSubFrames);\n  };\n  /**\n   * Get animation duration\n   * @param inFrames\n   */\n  var getDuration = function getDuration(inFrames) {\n    var _a;\n    return (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.getDuration(inFrames);\n  };\n  /**\n   * Destroy animation\n   */\n  var destroy = function destroy() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Removing the reference to the animation so separate cleanups are skipped.\n    // Without it the internal `lottie-react` instance throws exceptions as it already cleared itself on destroy.\n    animationInstanceRef.current = undefined;\n  };\n  /*\n        ======================================\n            LOTTIE\n        ======================================\n     */\n  /**\n   * Load a new animation, and if it's the case, destroy the previous one\n   * @param {Object} forcedConfigs\n   */\n  var loadAnimation = function loadAnimation() {\n    var forcedConfigs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _a;\n    // Return if the container ref is null\n    if (!animationContainer.current) {\n      return;\n    }\n    // Destroy any previous instance\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Build the animation configuration\n    var config = _objectSpread2(_objectSpread2(_objectSpread2({}, props), forcedConfigs), {}, {\n      container: animationContainer.current\n    });\n    // Save the animation instance\n    animationInstanceRef.current = lottie__default[\"default\"].loadAnimation(config);\n    setAnimationLoaded(!!animationInstanceRef.current);\n    // Return a function that will clean up\n    return function () {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n      animationInstanceRef.current = undefined;\n    };\n  };\n  /**\n   * (Re)Initialize when animation data changed\n   */\n  React.useEffect(function () {\n    var onUnmount = loadAnimation();\n    // Clean up on unmount\n    return function () {\n      return onUnmount === null || onUnmount === void 0 ? void 0 : onUnmount();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animationData, loop]);\n  // Update the autoplay state\n  React.useEffect(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    animationInstanceRef.current.autoplay = !!autoplay;\n  }, [autoplay]);\n  // Update the initial segment state\n  React.useEffect(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    // When null should reset to default animation length\n    if (!initialSegment) {\n      animationInstanceRef.current.resetSegments(true);\n      return;\n    }\n    // If it's not a valid segment, do nothing\n    if (!Array.isArray(initialSegment) || !initialSegment.length) {\n      return;\n    }\n    // If the current position it's not in the new segment\n    // set the current position to start\n    if (animationInstanceRef.current.currentRawFrame < initialSegment[0] || animationInstanceRef.current.currentRawFrame > initialSegment[1]) {\n      animationInstanceRef.current.currentRawFrame = initialSegment[0];\n    }\n    // Update the segment\n    animationInstanceRef.current.setSegment(initialSegment[0], initialSegment[1]);\n  }, [initialSegment]);\n  /*\n        ======================================\n            EVENTS\n        ======================================\n     */\n  /**\n   * Reinitialize listener on change\n   */\n  React.useEffect(function () {\n    var partialListeners = [{\n      name: \"complete\",\n      handler: onComplete\n    }, {\n      name: \"loopComplete\",\n      handler: onLoopComplete\n    }, {\n      name: \"enterFrame\",\n      handler: onEnterFrame\n    }, {\n      name: \"segmentStart\",\n      handler: onSegmentStart\n    }, {\n      name: \"config_ready\",\n      handler: onConfigReady\n    }, {\n      name: \"data_ready\",\n      handler: onDataReady\n    }, {\n      name: \"data_failed\",\n      handler: onDataFailed\n    }, {\n      name: \"loaded_images\",\n      handler: onLoadedImages\n    }, {\n      name: \"DOMLoaded\",\n      handler: onDOMLoaded\n    }, {\n      name: \"destroy\",\n      handler: onDestroy\n    }];\n    var listeners = partialListeners.filter(function (listener) {\n      return listener.handler != null;\n    });\n    if (!listeners.length) {\n      return;\n    }\n    var deregisterList = listeners.map(\n    /**\n     * Handle the process of adding an event listener\n     * @param {Listener} listener\n     * @return {Function} Function that deregister the listener\n     */\n    function (listener) {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener(listener.name, listener.handler);\n      // Return a function to deregister this listener\n      return function () {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(listener.name, listener.handler);\n      };\n    });\n    // Deregister listeners on unmount\n    return function () {\n      deregisterList.forEach(function (deregister) {\n        return deregister();\n      });\n    };\n  }, [onComplete, onLoopComplete, onEnterFrame, onSegmentStart, onConfigReady, onDataReady, onDataFailed, onLoadedImages, onDOMLoaded, onDestroy]);\n  /**\n   * Build the animation view\n   */\n  var View = /*#__PURE__*/React__default[\"default\"].createElement(\"div\", _objectSpread2({\n    style: style,\n    ref: animationContainer\n  }, rest));\n  return {\n    View: View,\n    play: play,\n    stop: stop,\n    pause: pause,\n    setSpeed: setSpeed,\n    goToAndStop: goToAndStop,\n    goToAndPlay: goToAndPlay,\n    setDirection: setDirection,\n    playSegments: playSegments,\n    setSubframe: setSubframe,\n    getDuration: getDuration,\n    destroy: destroy,\n    animationContainerRef: animationContainer,\n    animationLoaded: animationLoaded,\n    animationItem: animationInstanceRef.current\n  };\n};\n\n// helpers\nfunction getContainerVisibility(container) {\n  var _container$getBoundin = container.getBoundingClientRect(),\n    top = _container$getBoundin.top,\n    height = _container$getBoundin.height;\n  var current = window.innerHeight - top;\n  var max = window.innerHeight + height;\n  return current / max;\n}\nfunction getContainerCursorPosition(container, cursorX, cursorY) {\n  var _container$getBoundin2 = container.getBoundingClientRect(),\n    top = _container$getBoundin2.top,\n    left = _container$getBoundin2.left,\n    width = _container$getBoundin2.width,\n    height = _container$getBoundin2.height;\n  var x = (cursorX - left) / width;\n  var y = (cursorY - top) / height;\n  return {\n    x: x,\n    y: y\n  };\n}\nvar useInitInteractivity = function useInitInteractivity(_ref) {\n  var wrapperRef = _ref.wrapperRef,\n    animationItem = _ref.animationItem,\n    mode = _ref.mode,\n    actions = _ref.actions;\n  React.useEffect(function () {\n    var wrapper = wrapperRef.current;\n    if (!wrapper || !animationItem || !actions.length) {\n      return;\n    }\n    animationItem.stop();\n    var scrollModeHandler = function scrollModeHandler() {\n      var assignedSegment = null;\n      var scrollHandler = function scrollHandler() {\n        var currentPercent = getContainerVisibility(wrapper);\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref2) {\n          var visibility = _ref2.visibility;\n          return visibility && currentPercent >= visibility[0] && currentPercent <= visibility[1];\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        if (action.type === \"seek\" && action.visibility && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var frameToGo = action.frames[0] + Math.ceil((currentPercent - action.visibility[0]) / (action.visibility[1] - action.visibility[0]) * action.frames[1]);\n          //! goToAndStop must be relative to the start of the current segment\n          animationItem.goToAndStop(frameToGo - animationItem.firstFrame - 1, true);\n        }\n        if (action.type === \"loop\") {\n          // Loop: Loop a given frames\n          if (assignedSegment === null) {\n            // if not playing any segments currently. play those segments and save to state\n            animationItem.playSegments(action.frames, true);\n            assignedSegment = action.frames;\n          } else {\n            // if playing any segments currently.\n            //check if segments in state are equal to the frames selected by action\n            if (assignedSegment !== action.frames) {\n              // if they are not equal. new segments are to be loaded\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            } else if (animationItem.isPaused) {\n              // if they are equal the play method must be called only if lottie is paused\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            }\n          }\n        }\n        if (action.type === \"play\" && animationItem.isPaused) {\n          // Play: Reset segments and continue playing full animation from current position\n          animationItem.resetSegments(true);\n          animationItem.play();\n        }\n        if (action.type === \"stop\") {\n          // Stop: Stop playback\n          animationItem.goToAndStop(action.frames[0] - animationItem.firstFrame - 1, true);\n        }\n      };\n      document.addEventListener(\"scroll\", scrollHandler);\n      return function () {\n        document.removeEventListener(\"scroll\", scrollHandler);\n      };\n    };\n    var cursorModeHandler = function cursorModeHandler() {\n      var handleCursor = function handleCursor(_x, _y) {\n        var x = _x;\n        var y = _y;\n        // Resolve cursor position if cursor is inside container\n        if (x !== -1 && y !== -1) {\n          // Get container cursor position\n          var pos = getContainerCursorPosition(wrapper, x, y);\n          // Use the resolved position\n          x = pos.x;\n          y = pos.y;\n        }\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref3) {\n          var position = _ref3.position;\n          if (position && Array.isArray(position.x) && Array.isArray(position.y)) {\n            return x >= position.x[0] && x <= position.x[1] && y >= position.y[0] && y <= position.y[1];\n          }\n          if (position && !Number.isNaN(position.x) && !Number.isNaN(position.y)) {\n            return x === position.x && y === position.y;\n          }\n          return false;\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        // Process action types:\n        if (action.type === \"seek\" && action.position && Array.isArray(action.position.x) && Array.isArray(action.position.y) && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var xPercent = (x - action.position.x[0]) / (action.position.x[1] - action.position.x[0]);\n          var yPercent = (y - action.position.y[0]) / (action.position.y[1] - action.position.y[0]);\n          animationItem.playSegments(action.frames, true);\n          animationItem.goToAndStop(Math.ceil((xPercent + yPercent) / 2 * (action.frames[1] - action.frames[0])), true);\n        }\n        if (action.type === \"loop\") {\n          animationItem.playSegments(action.frames, true);\n        }\n        if (action.type === \"play\") {\n          // Play: Reset segments and continue playing full animation from current position\n          if (animationItem.isPaused) {\n            animationItem.resetSegments(false);\n          }\n          animationItem.playSegments(action.frames);\n        }\n        if (action.type === \"stop\") {\n          animationItem.goToAndStop(action.frames[0], true);\n        }\n      };\n      var mouseMoveHandler = function mouseMoveHandler(ev) {\n        handleCursor(ev.clientX, ev.clientY);\n      };\n      var mouseOutHandler = function mouseOutHandler() {\n        handleCursor(-1, -1);\n      };\n      wrapper.addEventListener(\"mousemove\", mouseMoveHandler);\n      wrapper.addEventListener(\"mouseout\", mouseOutHandler);\n      return function () {\n        wrapper.removeEventListener(\"mousemove\", mouseMoveHandler);\n        wrapper.removeEventListener(\"mouseout\", mouseOutHandler);\n      };\n    };\n    switch (mode) {\n      case \"scroll\":\n        return scrollModeHandler();\n      case \"cursor\":\n        return cursorModeHandler();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mode, animationItem]);\n};\nvar useLottieInteractivity = function useLottieInteractivity(_ref4) {\n  var actions = _ref4.actions,\n    mode = _ref4.mode,\n    lottieObj = _ref4.lottieObj;\n  var animationItem = lottieObj.animationItem,\n    View = lottieObj.View,\n    animationContainerRef = lottieObj.animationContainerRef;\n  useInitInteractivity({\n    actions: actions,\n    animationItem: animationItem,\n    mode: mode,\n    wrapperRef: animationContainerRef\n  });\n  return View;\n};\n\nvar _excluded = [\"style\", \"interactivity\"];\nvar Lottie = function Lottie(props) {\n  var _a, _b, _c;\n  var style = props.style,\n    interactivity = props.interactivity,\n    lottieProps = _objectWithoutProperties(props, _excluded);\n  /**\n   * Initialize the 'useLottie' hook\n   */\n  var _useLottie = useLottie(lottieProps, style),\n    View = _useLottie.View,\n    play = _useLottie.play,\n    stop = _useLottie.stop,\n    pause = _useLottie.pause,\n    setSpeed = _useLottie.setSpeed,\n    goToAndStop = _useLottie.goToAndStop,\n    goToAndPlay = _useLottie.goToAndPlay,\n    setDirection = _useLottie.setDirection,\n    playSegments = _useLottie.playSegments,\n    setSubframe = _useLottie.setSubframe,\n    getDuration = _useLottie.getDuration,\n    destroy = _useLottie.destroy,\n    animationContainerRef = _useLottie.animationContainerRef,\n    animationLoaded = _useLottie.animationLoaded,\n    animationItem = _useLottie.animationItem;\n  /**\n   * Make the hook variables/methods available through the provided 'lottieRef'\n   */\n  React.useEffect(function () {\n    if (props.lottieRef) {\n      props.lottieRef.current = {\n        play: play,\n        stop: stop,\n        pause: pause,\n        setSpeed: setSpeed,\n        goToAndPlay: goToAndPlay,\n        goToAndStop: goToAndStop,\n        setDirection: setDirection,\n        playSegments: playSegments,\n        setSubframe: setSubframe,\n        getDuration: getDuration,\n        destroy: destroy,\n        animationContainerRef: animationContainerRef,\n        animationLoaded: animationLoaded,\n        animationItem: animationItem\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [(_a = props.lottieRef) === null || _a === void 0 ? void 0 : _a.current]);\n  return useLottieInteractivity({\n    lottieObj: {\n      View: View,\n      play: play,\n      stop: stop,\n      pause: pause,\n      setSpeed: setSpeed,\n      goToAndStop: goToAndStop,\n      goToAndPlay: goToAndPlay,\n      setDirection: setDirection,\n      playSegments: playSegments,\n      setSubframe: setSubframe,\n      getDuration: getDuration,\n      destroy: destroy,\n      animationContainerRef: animationContainerRef,\n      animationLoaded: animationLoaded,\n      animationItem: animationItem\n    },\n    actions: (_b = interactivity === null || interactivity === void 0 ? void 0 : interactivity.actions) !== null && _b !== void 0 ? _b : [],\n    mode: (_c = interactivity === null || interactivity === void 0 ? void 0 : interactivity.mode) !== null && _c !== void 0 ? _c : \"scroll\"\n  });\n};\n\nObject.defineProperty(exports, \"LottiePlayer\", ({\n  enumerable: true,\n  get: function () { return lottie__default[\"default\"]; }\n}));\nexports[\"default\"] = Lottie;\nexports.useLottie = useLottie;\nexports.useLottieInteractivity = useLottieInteractivity;\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lottie-react/build/index.js\n");

/***/ })

};
;